package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"time"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/api/handlers"
	customMiddleware "a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/api/middleware"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/commandhandlers"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core/aliasgenerator"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core/fingerprint"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core/masking"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/infrastructure/config"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/monitoring"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/queryhandlers"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
	"github.com/go-chi/chi/v5"
	chiMiddleware "github.com/go-chi/chi/v5/middleware"
	"github.com/go-playground/validator/v10"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog"
)

type RouterConfig struct {
	AliasRepository  repository.AliasRepository
	Logger           *zerolog.Logger
	AppConfig        *config.AppConfig
	ServerConfig     *config.ServerConfig
	Validator        *validator.Validate
	MonitoringConfig *config.MonitoringConfig
}

type Router struct {
	*chi.Mux
	config RouterConfig
}

func NewRouter(config RouterConfig) *Router {
	r := chi.NewRouter()

	setupMiddleware(r, config)

	router := &Router{
		Mux:    r,
		config: config,
	}

	router.setupRoutes()

	return router
}

func setupMiddleware(r *chi.Mux, config RouterConfig) {
	r.Use(chiMiddleware.RequestID)
	r.Use(chiMiddleware.RealIP)

	r.Use(func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()

			// Create request logger with context
			logger := config.Logger.With().Fields(map[string]interface{}{
				"method":      r.Method,
				"url":         r.URL.String(),
				"user_agent":  r.UserAgent(),
				"request_id":  chiMiddleware.GetReqID(r.Context()),
				"remote_addr": r.RemoteAddr,
			}).Logger()

			// Add logger to request context
			ctx := logger.WithContext(r.Context())
			r = r.WithContext(ctx)

			// Wrap response writer to capture status code
			ww := chiMiddleware.NewWrapResponseWriter(w, r.ProtoMajor)

			defer func() {
				// log only if status is not 200 or 201
				if ww.Status() != 200 && ww.Status() != 201 {
					duration := time.Since(start)
					logger.Info().
						Int("status", ww.Status()).
						Int("bytes", ww.BytesWritten()).
						Dur("duration", duration).
						Msg("HTTP request completed")
				}
			}()

			next.ServeHTTP(ww, r)
		})
	})

	// Recovery middleware - recovers from panics and logs errors
	r.Use(chiMiddleware.Recoverer)
	// Timeout middleware - sets a timeout for requests
	r.Use(chiMiddleware.Timeout(config.ServerConfig.ReadTimeout))
	// Compress reponses
	r.Use(chiMiddleware.Compress(5))
	// Heartbeat middleware - checks if the service is alive
	r.Use(chiMiddleware.Heartbeat("/heartbeat"))

	if config.MonitoringConfig.Enabled {
		r.Use(func(next http.Handler) http.Handler {
			return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

				path := r.URL.Path

				if path == "/metrics" || path == "/health" || path == "/heartbeat" || path == "/" {
					next.ServeHTTP(w, r)
					return
				}

				start := time.Now()
				ww := chiMiddleware.NewWrapResponseWriter(w, r.ProtoMajor)
				//   timer := prometheus.NewTimer(monitoring.HttpRequestDurationMs.WithLabelValues(method, path, status))
				//   defer timer.ObserveDuration()
				next.ServeHTTP(ww, r)

				status := fmt.Sprintf("%d", ww.Status())
				method := r.Method

				// Get templated path, to prevent high cardinality for paths like /aliases/123, /aliases/456
				routeCtx := chi.RouteContext(r.Context())
				templatedPath := routeCtx.RoutePattern()

				// Record metrics for http total requests and request duration
				monitoring.HttpRequestsTotal.WithLabelValues(method, templatedPath, status).Inc()
				monitoring.HttpRequestDurationMs.WithLabelValues(method, templatedPath, status).Observe(float64(time.Since(start).Milliseconds()))
			})
		})
	}

	// Security headers
	r.Use(func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("X-Content-Type-Options", "nosniff")
			if config.ServerConfig.Build == "production" {
				w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
			}

			next.ServeHTTP(w, r)
		})
	})

	// Validation middleware
	r.Use(customMiddleware.ValidationMiddleware(config.Validator))

	// Profiling middleware - for debugging
	if config.ServerConfig.Debug {
		r.Mount("/debug", chiMiddleware.Profiler())
	}
}

func (router *Router) setupRoutes() {
	router.Get("/", router.homeHandler)
	router.Get("/health", router.healthHandler)

	if router.config.MonitoringConfig.Enabled {
		router.Handle("/metrics", promhttp.Handler())
	}

	domains := router.config.AppConfig
	// Service handler dependencies
	aliasGenerator := aliasgenerator.NewAliasGenerator(domains.RelayDomain, domains.ClientDomain, domains.PartnerDomain, domains.InternalDomain)
	emailMasker := masking.NewEmailMasker()
	fingerprintGenerator := fingerprint.NewFingerprintGenerator()

	// Service handlers commands and queries
	// Commands
	createAliasHandler := commandhandlers.NewCreateAliasCommandHandler(router.config.AliasRepository, aliasGenerator, emailMasker, fingerprintGenerator)
	updateAliasSenderEmailHandler := commandhandlers.NewUpdateAliasSenderEmailCommandHandler(router.config.AliasRepository, emailMasker)
	// Queries
	getAliasByIDHandler := queryhandlers.NewGetAliasByIDQueryHandler(router.config.AliasRepository)
	getAliasByContextFingerprintHandler := queryhandlers.NewGetAliasByContextFingerprintQueryHandler(router.config.AliasRepository)
	getAliasesByLegacyFingerprintHandler := queryhandlers.NewGetAliasesByLegacyFingerprintQueryHandler(router.config.AliasRepository)
	getAliasByLocatorHandler := queryhandlers.NewGetAliasByLocatorAndPurposeQueryHandler(router.config.AliasRepository)
	getAliasesByOriginalRecipientEmailHandler := queryhandlers.NewGetAliasesByOriginalRecipientEmailQueryHandler(router.config.AliasRepository, fingerprintGenerator)
	getAliasesByHostUserIDHandler := queryhandlers.NewGetAliasesByHostUserIDQueryHandler(router.config.AliasRepository)

	router.Route("/api", func(r chi.Router) {
		r.Post("/alias", handlers.CreateAliasHTTPHandler(createAliasHandler, router.config.Logger, domains.ExcludedDomains))
		r.Put("/alias/sender_email", handlers.UpdateAliasSenderEmailHTTPHandler(updateAliasSenderEmailHandler, router.config.Logger))
		r.Get("/alias/{aliasID}", handlers.GetAliasByIDHTTPHandler(getAliasByIDHandler, router.config.Logger))
		r.Get("/alias/context/{contextFingerprint}", handlers.GetAliasByContextFingerprintHTTPHandler(getAliasByContextFingerprintHandler, router.config.Logger))
		r.Get("/aliases/fingerprint", handlers.GetAliasesByLegacyFingerprintHTTPHandler(getAliasesByLegacyFingerprintHandler, router.config.Logger))
		r.Get("/alias/locator/{locator}", handlers.GetAliasByLocatorAndPurposeHTTPHandler(getAliasByLocatorHandler, router.config.Logger))
		r.Get("/aliases/original_recipient", handlers.GetAliasesByOriginalRecipientEmailHTTPHandler(getAliasesByOriginalRecipientEmailHandler, router.config.Logger))
		r.Get("/aliases/host_user/{hostUserID}", handlers.GetAliasesByHostUserIDHTTPHandler(getAliasesByHostUserIDHandler, router.config.Logger))
	})
}

type HealthResponse struct {
	Status      string            `json:"status"`
	Service     string            `json:"service"`
	Version     string            `json:"version"`
	Environment string            `json:"environment"`
	Timestamp   string            `json:"timestamp"`
	Uptime      string            `json:"uptime"`
	Checks      map[string]string `json:"checks"`
}

var startTime = time.Now()

// healthHandler provides comprehensive health information
func (router *Router) healthHandler(w http.ResponseWriter, r *http.Request) {
	logger := zerolog.Ctx(r.Context())

	checks := make(map[string]string)

	health := HealthResponse{
		Status:      "ok",
		Service:     router.config.ServerConfig.ServiceName,
		Version:     router.config.AppConfig.Version,
		Environment: router.config.ServerConfig.Build,
		Timestamp:   time.Now().UTC().Format(time.RFC3339),
		Uptime:      time.Since(startTime).String(),
		Checks:      checks,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(health); err != nil {
		logger.Error().Err(err).Msg("Failed to encode health response")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]interface{}{"error": "Internal Server Error: Failed to encode health response"})
		return
	}

	logger.Debug().Msg("Health check completed")
}

// GracefulShutdown performs graceful shutdown of the router
func (router *Router) GracefulShutdown(ctx context.Context, server *http.Server) error {
	logger := router.config.Logger

	// Create shutdown context with timeout
	shutdownCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Shutdown the server
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Error().Err(err).Msg("Server forced to shutdown")
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	logger.Info().Msg("Server gracefully stopped")
	return nil
}

// homeHandler provides API documentation and information
func (router *Router) homeHandler(w http.ResponseWriter, r *http.Request) {
	html := fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Mail Alias Management API</title>    
</head>
<body>
    <div id="master">
        <div id="top">
            <a href="http://www.pricetravel.com">
                <img alt="logoPriceTravel" height="42" src="https://3.cdnpt.com/images/PT_Logo_404x60.png"/>
            </a>
        </div>
        <div id="center">
            PriceTravel Mail Alias Management API<br />
            <br />
            <strong>System Information:</strong><br />
            <ul>
                <li>Version: %s</li>
                <li>Environment: %s</li>
                <li>Go Version: %s</li>
                <li>OS/Arch: %s/%s</li>
                <li>Framework: Chi Router v5</li>
            </ul>
            <br />
            <strong>Documentation:</strong><br />
            <ul>
                <li><a href="https://a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management" target="_blank">📖 Repository</a></li>
            </ul>
            <br />
            <strong>Available Endpoints:</strong><br />
            <ul>
                <li>GET /health - Health check</li>
                <li>GET /heartbeat - Simple heartbeat</li>
                <li>POST /api/alias - Create new alias</li>
                <li>GET /api/alias/{aliasID} - Get alias by ID</li>
                <li>GET /api/alias/context/{contextFingerprint} - Get alias by context</li>
                <li>GET /api/aliases/fingerprint - Get aliases by legacy fingerprint</li>
                <li>GET /api/alias/locator/{locator} - Get alias by locator</li>
                <li>GET /api/aliases/original_recipient - Get aliases by recipient</li>
                <li>GET /api/aliases/host_user/{hostUserID} - Get aliases by host user</li>
                <li>POST /auth/token - Generate JWT token</li>
                <li>POST /auth/token/refresh - Refresh JWT token</li>
            </ul>
        </div>
        <div id="bottom">
            © Price Res, SAPI de CV Todos los derechos reservados. All rights reserved.
        </div>
    </div>
</body>
</html>`,
		router.config.AppConfig.Version,
		router.config.ServerConfig.Build,
		runtime.Version(),
		runtime.GOOS,
		runtime.GOARCH)

	w.Header().Set("Content-Type", "text/html")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(html))
}
