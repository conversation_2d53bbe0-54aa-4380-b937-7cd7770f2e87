# 📧 Mail Alias Management system (AMS)

![Project Status](https://img.shields.io/badge/status-design/development-blue)
![Language](https://img.shields.io/badge/language-Go-blue)
![Architecture](https://img.shields.io/badge/architecture-Microservice%20%7C%20CQRS-green)

## 🚀 Requirements

### System Requirements
- Docker & Docker Compose
- AWS Account with DynamoDB access
- Go 1.24.3+ (for local development)

### AWS Configuration
- DynamoDB table: `EmailAliases`
- Required AWS credentials configured locally (`~/.aws/`)
- IAM permissions for DynamoDB read/write operations

## 🐳 Running with Docker Compose

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd mail_alias_management

# Ensure AWS credentials are configured
aws configure

# Start the service
docker-compose up -d

# Check service health
curl http://localhost:5000/health

### Prerequisites
```bash
# Install Go 1.24.3+
go version

# Install dependencies
go mod download

# Configure AWS credentials
aws configure
```

### Running Locally
```bash
# Build and run
go run cmd/server/main.go

# Or build binary
go build -o main cmd/server/main.go
./main
```

### API Endpoints
- `GET /health` - Health check
- `GET /heartbeat` - Simple heartbeat
- `POST /api/alias` - Create new alias
- `GET /api/alias/{aliasID}` - Get alias by ID
- `POST /auth/token` - Generate JWT token (if auth enabled)

###Data Model (Alias Mapping Database)

The `Alias Mapping Database` is central to the AMS. It stores the comprehensive context for each generated alias.

Here's a simplified view of the `aliases` / `EmailAliases` table structure:

| Column Name                | Data Type      | Description                                                                  | Nullable? |
| :------------------------- | :------------- | :--------------------------------------------------------------------------- | :-------- |
| `alias_id`                 | `VARCHAR(255)` | Unique alias string (Primary Key)                                            | No        |
| `original_recipient_email` | `VARCHAR(255)` | Actual email of the primary recipient of alias-bound mail                    | No        |
| `original_sender_email`    | `VARCHAR(255)` | Actual email of the initiator of communication (other side of bidirectional) | Yes       |
| `masked_recipient_email`   | `VARCHAR(255)` | Privacy-preserving masked version of `original_recipient_email`              | No        |
| `masked_sender_email`      | `VARCHAR(255)` | Privacy-preserving masked version of `original_sender_email`                 | No        |
| `locator`                  | `VARCHAR(50)`  | Booking Locator (e.g., BKID)                                                 | Yes       |
| `guest_user_id`            | `VARCHAR(50)`  | Pricetravel.com internal Guest User ID (canonical ID)                        | Yes       |
| `host_user_id`             | `VARCHAR(50)`  | Pricetravel.com internal Host/Hotel User ID (canonical ID)                   | Yes       |
| `fingerprint`              | `VARCHAR(255)` | Hash of original_recipient_email or initializer                              | No        |
| `context_fingerprint`      | `VARCHAR(255)` | Hash of key contextual fields for uniqueness checks (snd,rec,porp,locl)      | No        |
| `purpose`                  | `VARCHAR(50)`  | **Explicit reason for alias creation** (e.g., `guest_host_chat`)             | No        |
| `channel`                  | `VARCHAR(50)`  | Origin of booking/communication (e.g., `MOBILE_APP`)                         | Yes       |
| `communication_direction`  | `VARCHAR(10)`  | `outbound`, `bidirectional`                                       | No        |
| `creator_service`          | `VARCHAR(50)`  | Internal service that requested alias creation                               | Yes       |
| `created_at`               | `TIMESTAMP`    | Timestamp of creation                                                        | No        |
| `expires_at`               | `TIMESTAMP`    | Auto-expiration timestamp for retention                                      | Yes       |
| `status`                   | `VARCHAR(20)`  | `active`, `inactive`, `pending_deletion`, etc.                               | No        |
| `last_used_at`             | `TIMESTAMP`    | Last time alias was used                                                     | Yes       |
| *`metadata`                | `JSONB`/`TEXT` | Flexible field for additional context                                        | Yes       |