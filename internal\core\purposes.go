package core

import (
	"fmt"
	"strings"
)

// Defines constants for the different purposes of an alias
const (
	// Default Bidirectional Communication Purposes (Require original_sender_email)
	PurposeGuestHostChatHotel        = "guest_host_chat_hotel"         // Guest <> Hotel (Traditional hotel booking)
	PurposeGuestHostChatVacationHome = "guest_host_chat_vacation_home" // Guest <> Host (Private/vacation home booking)
	PurposeGuestHostChatAirline      = "guest_host_chat_airline"       // Guest <> Airline (e.g., direct queries on flights)
	PurposeCustomerSupportThread     = "customer_support_thread"       // Guest/Host <> pricetravel.com Support
	PurposeInternalCommunication     = "internal_communication"        // pricetravel.com <> pricetravel.com

	// Outbound / One-Way Purposes (No original_sender_email required)
	PurposeSystemBookingConfirmation    = "system_booking_confirmation"     // pricetravel.com -> Guest: Booking receipt
	PurposeSystemFlightConfirmation     = "system_flight_confirmation"      // pricetravel.com -> Guest: Flight-specific booking confirmation
	PurposeSystemReviewRequest          = "system_review_request"           // pricetravel.com -> Guest: Request for review post-stay
	PurposeSystemPaymentNotification    = "system_payment_notification"     // pricetravel.com -> Guest: Payment success/failure/refund
	PurposeSystemFlightStatusAlert      = "system_flight_status_alert"      // pricetravel.com -> Guest: Flight delay/cancellation
	PurposeSystemPreCheckinInstructions = "system_pre_checkin_instructions" // pricetravel.com -> Guest: Pre-checkin instructions

	// Other potential purposes (less common but possible)
	PurposeMarketingCampaign = "marketing_campaign" // pricetravel.com -> Guest: Specific marketing email
)

// AllPurposes is a list of all possible purposes
var AllPurposes = []string{
	PurposeGuestHostChatHotel,
	PurposeGuestHostChatVacationHome,
	PurposeGuestHostChatAirline,
	PurposeCustomerSupportThread,
	PurposeSystemBookingConfirmation,
	PurposeInternalCommunication,
	PurposeSystemFlightConfirmation,
	PurposeSystemReviewRequest,
	PurposeSystemPaymentNotification,
	PurposeSystemFlightStatusAlert,
	PurposeSystemPreCheckinInstructions,
	PurposeMarketingCampaign,
}

// AllPurposesString returns all purposes as a space-separated string for validation tags
func AllPurposesString() string {
	return strings.Join(AllPurposes, " ")
}

// GetCommunicationDirectionForPurpose determines the communication direction based on the purpose.
// This function would be used inside your CreateAliasCommandHandler.
func GetCommunicationDirectionForPurpose(purpose string) (string, error) {
	switch purpose {
	case PurposeGuestHostChatHotel,
		PurposeGuestHostChatVacationHome,
		PurposeGuestHostChatAirline,
		PurposeCustomerSupportThread,
		PurposeInternalCommunication:
		return "bidirectional", nil
	case PurposeSystemBookingConfirmation,
		PurposeSystemFlightConfirmation,
		PurposeSystemReviewRequest,
		PurposeSystemPaymentNotification,
		PurposeSystemFlightStatusAlert,
		PurposeSystemPreCheckinInstructions,
		PurposeMarketingCampaign:
		return "outbound", nil
	default:
		return "", fmt.Errorf("unknown purpose '%s': cannot determine communication direction", purpose)
	}
}

// IsPurposeRequiresSenderEmail checks if a purpose requires original_sender_email to be non-nil.
// This would be used in validation within CreateAliasCommandHandler.
func IsPurposeRequiresSenderEmail(purpose string) bool {
	switch purpose {
	case PurposeGuestHostChatVacationHome:
		return true
	default:
		return false
	}
}

func isPurposeRequiresLocator(purpose string) bool {
	switch purpose {
	case PurposeGuestHostChatHotel,
		PurposeSystemBookingConfirmation,
		PurposeSystemFlightConfirmation,
		PurposeSystemReviewRequest,
		PurposeSystemPaymentNotification,
		PurposeSystemFlightStatusAlert:
		return true // These MUST be tied to a specific transaction
	case PurposeGuestHostChatVacationHome, // If vacation home chat *can* exist pre-booking
		PurposeCustomerSupportThread, // Support can be generic
		PurposeMarketingCampaign:     // Marketing can be generic
		return false // Locator is optional for these
	default:
		return true // Default to required for safety, or error on unknown purpose
	}
}

func GetAliasPrefixByPurpose(purpose string) string {
	switch purpose {
	case PurposeGuestHostChatHotel, PurposeGuestHostChatVacationHome, PurposeGuestHostChatAirline, PurposeCustomerSupportThread:
		return "chat"
	case PurposeSystemBookingConfirmation, PurposeSystemFlightConfirmation:
		return "confirm"
	case PurposeSystemReviewRequest:
		return "review"
	case PurposeSystemPaymentNotification:
		return "payment"
	case PurposeSystemFlightStatusAlert:
		return "status"
	case PurposeSystemPreCheckinInstructions:
		return "pre"
	case PurposeMarketingCampaign:
		return "marketing"
	case PurposeInternalCommunication:
		return "internal"
	default:
		return "alias" // fallback (technically should not fallback here due to validation)
	}
}
