package config

import (
	"fmt"
	"time"

	"github.com/go-playground/validator/v10"
)

// Config holds all application configuration
type Config struct {
	Server     ServerConfig     `mapstructure:"server"`
	App        AppConfig        `mapstructure:"app"`
	Monitoring MonitoringConfig `mapstructure:"monitoring"`
	DynamoDB   DynamoDBConfig   `mapstructure:"dynamodb"`
}

type DynamoDBConfig struct {
	Region         string           `mapstructure:"region" validate:"required"`
	Table          string           `mapstructure:"table" validate:"required"`
	CustomEndpoint string           `mapstructure:"customEndpoint"`
	EnableTTL      bool             `mapstructure:"enableTTL"`
	TTLGraceDays   int              `mapstructure:"ttlGraceDays"`
	Encryption     EncryptionConfig `mapstructure:"encryption"`
}

type EncryptionConfig struct {
	Enabled bool              `mapstructure:"enabled" validate:"required"`
	Type    string            `mapstructure:"type" validate:"required_if=Enabled true,oneof=aes"`
	Keys    map[string]string `mapstructure:"keys" validate:"required_if=Enabled true"` // Base64 encoded AES key
}

type ServerConfig struct {
	ServiceName     string        `mapstructure:"serviceName" validate:"required"`
	Build           string        `mapstructure:"build" validate:"required,oneof=development production"`
	Port            int           `mapstructure:"port" validate:"required"`
	Debug           bool          `mapstructure:"debug"`
	LoggerLevel     string        `mapstructure:"loggerLevel" validate:"required,oneof=trace debug info warn error fatal panic"`
	ShutdownTimeout time.Duration `mapstructure:"shutdownTimeout" validate:"required"`
	ReadTimeout     time.Duration `mapstructure:"readTimeout" validate:"required"`
	WriteTimeout    time.Duration `mapstructure:"writeTimeout" validate:"required"`
	IdleTimeout     time.Duration `mapstructure:"idleTimeout" validate:"required"`
}

type AppConfig struct {
	Version         string   `mapstructure:"version" validate:"required"`
	RelayDomain     string   `mapstructure:"relayDomain" validate:"required"`
	ClientDomain    string   `mapstructure:"clientDomain" validate:"required"`
	PartnerDomain   string   `mapstructure:"partnerDomain" validate:"required"`
	InternalDomain  string   `mapstructure:"internalDomain" validate:"required"`
	ExcludedDomains []string `mapstructure:"excludedDomains"`
}

type MonitoringConfig struct {
	Enabled bool `mapstructure:"enabled"` // Enable monitoring
}

func LoadConfig(loader ConfigLoader) (*Config, error) {
	if loader == nil {
		loader = DefaultViperLoader()
	}

	var config Config
	if err := loader.Load(&config); err != nil {
		return nil, fmt.Errorf("failed to load config %w", err)
	}

	validate := validator.New()
	if err := validate.Struct(config); err != nil {
		return nil, fmt.Errorf("config validation failed %w", err)
	}

	return &config, nil
}

func LoadDefaultConfig() (*Config, error) {
	return LoadConfig(nil)
}
