package aliasgenerator

import (
	"crypto/rand"
	"fmt"
	"strings"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
)

type AliasGenerator struct {
	RelayDomain    string // e.g., "relay.pricetravel.com"
	ClientDomain   string // e.g., "client.pricetravel.com"
	PartnerDomain  string // e.g., "partner.pricetravel.com"
	InternalDomain string // e.g., "privaterelay.pricetravel.com"
}

func NewAliasGenerator(relayDomain, clientDomain, partnerDomain, internalDomain string) *AliasGenerator {
	return &AliasGenerator{
		RelayDomain:    relayDomain,
		ClientDomain:   clientDomain,
		PartnerDomain:  partnerDomain,
		InternalDomain: internalDomain,
	}
}

// GenerateAliasID generates a unique email alias string based on the given purpose and context.
// It combines a purpose prefix, a locator/reference, and a unique random suffix.
func (g *AliasGenerator) GenerateAliasID(purpose, locator string) (string, error) {
	// Get prefix from centralized purpose definitions
	prefix := core.GetAliasPrefixByPurpose(purpose)
	if prefix == "alias" && !g.isKnownPurpose(purpose) {
		return "", fmt.Errorf("unknown purpose '%s': please use one of the defined purposes from core.AllPurposes", purpose)
	}

	// Generate random suffix based on purpose requirements
	var domain = g.RelayDomain
	var uniqueSuffix string
	var err error

	switch purpose {
	case core.PurposeGuestHostChatHotel,
		core.PurposeGuestHostChatVacationHome,
		core.PurposeGuestHostChatAirline:
		uniqueSuffix, err = generateRandomAlphaNumeric(6) // 6 chars for chat aliases
		domain = g.ClientDomain
		if err != nil {
			return "", fmt.Errorf("failed to generate random suffix for %s alias: %w", purpose, err)
		}
	case core.PurposeSystemBookingConfirmation,
		core.PurposeSystemFlightConfirmation:
		// For confirmations, the booking locator usually provides enough uniqueness
		uniqueSuffix = "" // No additional random suffix needed
	case core.PurposeSystemReviewRequest:
		uniqueSuffix, err = generateRandomAlphaNumeric(4) // Shorter random for reviews
		if err != nil {
			return "", fmt.Errorf("failed to generate random suffix for %s alias: %w", purpose, err)
		}
	case core.PurposeCustomerSupportThread:
		uniqueSuffix, err = generateRandomAlphaNumeric(8) // Longer random for support
		if err != nil {
			return "", fmt.Errorf("failed to generate random suffix for %s alias: %w", purpose, err)
		}
	case core.PurposeSystemPaymentNotification,
		core.PurposeSystemFlightStatusAlert,
		core.PurposeSystemPreCheckinInstructions:
		uniqueSuffix, err = generateRandomAlphaNumeric(4) // Standard random for system notifications
		if err != nil {
			return "", fmt.Errorf("failed to generate random suffix for %s alias: %w", purpose, err)
		}
	case core.PurposeMarketingCampaign:
		uniqueSuffix, err = generateRandomAlphaNumeric(6) // Medium random for marketing
		if err != nil {
			return "", fmt.Errorf("failed to generate random suffix for %s alias: %w", purpose, err)
		}
	case core.PurposeInternalCommunication:
		uniqueSuffix, err = generateRandomAlphaNumeric(6) // Medium random for internal communication
		if err != nil {
			return "", fmt.Errorf("failed to generate random suffix for %s alias: %w", purpose, err)
		}
		domain = g.InternalDomain
	default:
		// Fallback for any new purposes not yet handled in suffix logic
		uniqueSuffix, err = generateRandomAlphaNumeric(8)
		if err != nil {
			return "", fmt.Errorf("failed to generate random suffix for %s alias: %w", purpose, err)
		}
	}

	// Construct the local part of the email alias
	var localPartBuilder strings.Builder
	localPartBuilder.WriteString(prefix)
	localPartBuilder.WriteString("-")
	localPartBuilder.WriteString(locator) // Locator is always part of the alias for context

	if uniqueSuffix != "" {
		localPartBuilder.WriteString("-")
		localPartBuilder.WriteString(uniqueSuffix)
	}

	// Final alias ID: localPart@domain
	return fmt.Sprintf("%s@%s", localPartBuilder.String(), domain), nil
}

// generateRandomAlphaNumeric generates a random string of specified length.
// Uses crypto/rand for cryptographically secure randomness.
func generateRandomAlphaNumeric(length int) (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	_, err := rand.Read(b) // Use crypto/rand for secure random
	if err != nil {
		return "", fmt.Errorf("failed to read random bytes: %w", err)
	}

	for i := 0; i < length; i++ {
		b[i] = charset[b[i]%byte(len(charset))]
	}
	return string(b), nil
}

// isKnownPurpose checks if the purpose is in the list of defined purposes
func (g *AliasGenerator) isKnownPurpose(purpose string) bool {
	for _, knownPurpose := range core.AllPurposes {
		if purpose == knownPurpose {
			return true
		}
	}
	return false
}

// Example usage in main.go or dependency injection:
/*
func main() {
    aliasGenerator := aliasgenerator.NewAliasGenerator(cfg.Alias.Domain)

    // Now inject aliasGenerator into your CreateAliasCommandHandler
    // createAliasHandler := commandhandlers.NewCreateAliasCommandHandler(aliasRepo, aliasGenerator, logger)
}
*/
