package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/go-playground/validator/v10"
	"github.com/rs/zerolog"
)

type ApiResponse struct {
	Success bool           `json:"success"`
	Data    interface{}    `json:"data"`
	Error   *ErrorResponse `json:"error,omitempty"`
}

type ErrorResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func SendApiResponse(w http.ResponseWriter, status int, data interface{}, success bool, error *ErrorResponse, logger *zerolog.Logger) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	if !success || status >= 400 {
		logger.Error().Str("code", error.Code).Str("message", error.Message).Int("status", status).Msg("API Response")
	}

	if err := json.NewEncoder(w).Encode(ApiResponse{
		Success: success,
		Data:    data,
		Error:   error,
	}); err != nil {
		logger.Error().Err(err).Msg("Failed to encode api response")
	}
}

func ValidateAndRespond(w http.ResponseWriter, validator *validator.Validate, data interface{}, logger *zerolog.Logger) bool {
	if err := validator.Struct(data); err != nil {
		SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
			Code:    "VALIDATION_ERROR",
			Message: err.Error(),
		}, logger)
		return false
	}
	return true
}
