package main

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"
)

type Migrator struct {
	db                  *Pool
	logger              *zerolog.Logger
	schema              string
	locatorTableName    string
	emailTokenTableName string
}

func NewMigrator(db *Pool, log *zerolog.Logger, schema string, locatorTableName string, emailTokenTableName string) *Migrator {
	return &Migrator{
		db:                  db,
		logger:              log,
		schema:              schema,
		locatorTableName:    locatorTableName,
		emailTokenTableName: emailTokenTableName,
	}
}

func (m *Migrator) GetEmailTokensPaginated(ctx context.Context, limit, offset int) ([]EmailTokenWithLocator, error) {
	if m.db == nil {
		return nil, fmt.Errorf("database pool is not initialized")
	}

	// SQL query with LEFT JOIN to get all email tokens, with optional tokenlocator data
	query := `
		SELECT  
			et.original_email, 
			et.mask, 
			et.token, 
			et.fingerprint, 
			et.date_created, 
			et.mode_creation,
			et.active,
			tl.locator,
			tl.channel
		FROM ` + m.schema + `.` + m.emailTokenTableName + ` et
		LEFT JOIN ` + m.schema + `.` + m.locatorTableName + ` tl ON et.token = tl.token
		ORDER BY et.date_created DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := m.db.Query(ctx, query, limit, offset)
	if err != nil {
		m.logger.Error().
			Err(err).
			Int("limit", limit).
			Int("offset", offset).
			Msg("Failed to execute paginated email tokens query with left join")
		return nil, fmt.Errorf("failed to query paginated email tokens with left join: %w", err)
	}
	defer rows.Close()

	var emailTokens []EmailTokenWithLocator
	rowCount := 0

	for rows.Next() {
		var token EmailTokenWithLocator
		var locator *int
		var channel *int

		err := rows.Scan(
			&token.OriginalEmail,
			&token.Mask,
			&token.Token,
			&token.Fingerprint,
			&token.DateCreated,
			&token.ModeCreation,
			&token.Active,
			&locator,
			&channel,
		)

		if err != nil {
			m.logger.Error().
				Err(err).
				Int("row_count", rowCount).
				Msg("Failed to scan email token row with left join")
			return nil, fmt.Errorf("failed to scan email token row %d with left join: %w", rowCount, err)
		}

		if locator != nil {
			token.Locator = *locator
		}

		if channel != nil {
			token.Channel = *channel
		}

		emailTokens = append(emailTokens, token)
		rowCount++
	}

	if err = rows.Err(); err != nil {
		m.logger.Error().Err(err).Msg("Error occurred during row iteration")
		return nil, fmt.Errorf("error during row iteration: %w", err)
	}

	return emailTokens, nil
}

func (m *Migrator) GetEmailTokensCount(ctx context.Context) (int, error) {
	if m.db == nil {
		return 0, fmt.Errorf("database pool is not initialized")
	}

	var count int
	// Use simple count from email_tokens since we want ALL tokens
	query := `SELECT COUNT(*) FROM ` + m.schema + `.` + m.emailTokenTableName + `;`

	err := m.db.QueryRow(ctx, query).Scan(&count)
	if err != nil {
		m.logger.Error().Err(err).Msg("Failed to get email tokens count")
		return 0, fmt.Errorf("failed to get email tokens count: %w", err)
	}

	return count, nil
}
