groups:
  - name: mail_alias_management
    rules:
      # High error rate alert
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"

      # High response time alert
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_ms_bucket[5m])) > 1000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"

      # Alias creation failures
      - alert: AliasCreationFailures
        expr: rate(alias_creation_failures_total[5m]) > 0.05
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "High alias creation failure rate"

      # Worker cleanup issues
      - alert: WorkerCleanupStalled
        expr: time() - worker_cleanup_cycles_total > 1800
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Cleanup worker appears stalled"
