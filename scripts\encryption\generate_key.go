package main

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"os"
)

func main() {
	// Generate 32-byte (256-bit) key for AES-256
	key := make([]byte, 32)
	_, err := rand.Read(key)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to generate random key: %v\n", err)
		os.Exit(1)
	}

	// Encode as base64
	encodedKey := base64.StdEncoding.EncodeToString(key)

	fmt.Println("Generated AES-256 encryption key:")
	fmt.Println("==================================")
	fmt.Println(encodedKey)
	fmt.Println()
	fmt.Println("Set this as your ENCRYPTION_KEY environment variable:")
	fmt.Printf("export ENCRYPTION_KEY=\"%s\"\n", encodedKey)
	fmt.Println()
	fmt.Println("For production, store this key securely in:")
	fmt.Println("- AWS Secrets Manager")
	fmt.Println("- HashiCorp Vault")
	fmt.Println("- Kubernetes Secrets")
	fmt.Println("- Environment variables (less secure)")
}
