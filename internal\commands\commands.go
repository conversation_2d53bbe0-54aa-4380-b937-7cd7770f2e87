package commands

import (
	"time"
)

// CreateAliasCommand represents a request to create a new email alias.
type CreateAliasCommand struct {
	// Fields representing the core identity of the alias
	OriginalRecipientEmail string  `json:"original_recipient_email" validate:"required,email"`
	OriginalSenderEmail    *string `json:"original_sender_email" validate:"omitempty,email"`

	Locator     string  `json:"locator" validate:"required,alphanum"`    // Booking Locator or similar
	GuestUserID *string `json:"guest_user_id" validate:"omitempty,uuid"` // Always required for guest-related aliases (shadow account if unregistered)
	HostUserID  *string `json:"host_user_id" validate:"omitempty,uuid"`  // Optional, not all aliases involve a host

	// Fields defining the alias's purpose and nature
	Purpose                string `json:"purpose" validate:"required"` // Validation handled with purposes.go in http_handlers.go
	Channel                *int   `json:"channel" validate:"omitempty,number"`
	CommunicationDirection string `json:"communication_direction" validate:"omitempty,oneof=outbound bidirectional"`

	CreatorService string `json:"creator_service" validate:"required"`

	ExpiresAt time.Time `json:"expires_at" validate:"required"`
	// CATALOG: product_type
	ProductType *string `json:"product_type" validate:"omitempty,oneof=none hotel transfer flight tour carr package service_charge bus reservation_process cruise insurance circuit generic administrative_fee credit_card_surcharge miscellany"`
}

type UpdateAliasSenderEmailCommand struct {
	AliasID     string `json:"alias_id" validate:"required,email"`
	SenderEmail string `json:"sender_email" validate:"required,email"`
}

// // UpdateAliasStatusCommand represents a request to change the status of an existing alias.
// type UpdateAliasStatusCommand struct {
// 	AliasID string `json:"alias_id" validate:"required,email"`
// 	Status  string `json:"status" validate:"required,oneof=active inactive suspended pending_deletion deleted"`
// 	// Additional fields like updater_service, reason, etc., could be added for audit
// }

// // UpdateAliasExpirationCommand represents a request to modify the expiration date of an alias.
// type UpdateAliasExpirationCommand struct {
// 	AliasID   string    `json:"alias_id" validate:"required,email"`
// 	ExpiresAt time.Time `json:"expires_at" validate:"required"` // Must be a valid future timestamp
// }

// // DeleteAliasByIDCommand represents a request to delete a single alias by its unique ID.
// // Used for targeted deletion, e.g., for testing or specific edge cases.
// type DeleteAliasByIDCommand struct {
// 	AliasID string `json:"alias_id" validate:"required,email"`
// }

// // DeleteAliasesByGuestUserIDCommand represents a request to delete all aliases
// // associated with a specific guest user. Crucial for Privacy "Right to Erasure".
// // This would typically trigger a background process.
// type DeleteAliasesByGuestUserIDCommand struct {
// 	GuestUserID string `json:"guest_user_id" validate:"required,uuid"`
// 	// An optional 'reason' field for auditing deletions might be useful here.
// }

// // DeleteAliasesByLocatorCommand represents a request to delete all aliases
// // associated with a specific locator (e.g., when a booking is cancelled).
// type DeleteAliasesByLocatorCommand struct {
// 	Locator string `json:"locator" validate:"required,alphanum"`
// 	Purpose string `json:"purpose" validate:"omitempty,alpha_dash"` // Optional: to narrow down deletion to specific types
// }
