package core

import (
	"time"
)

func FilterActiveAndNotExpiredAliases(aliases []*<PERSON>as) []*<PERSON><PERSON> {
	filteredAliases := make([]*<PERSON><PERSON>, 0)

	if len(aliases) == 0 {
		return filteredAliases
	}

	for _, alias := range aliases {
		if FilterActiveAndNotExpiredAlias(alias) {
			filteredAliases = append(filteredAliases, alias)
		}
	}
	return filteredAliases
}

func FilterActiveAndNotExpiredAlias(alias *Alias) bool {
	return alias.Status == "active" && alias.ExpiresAt.After(time.Now())
}
