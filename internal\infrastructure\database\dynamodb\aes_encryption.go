package dynamodb

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"log/slog"
	"strings"
)

const (
	// Use a prefix to deterministically identify encrypted data
	encryptionPrefix = "enc"
	// Separator for the structured format: enc:v1:<payload>
	separator = ":"
)

// AESEncryptionService provides AES-GCM encryption/decryption for sensitive data
type AESEncryptionService struct {
	// Store multiple GCM ciphers, keyed by version, for performance
	gcmInstances map[string]cipher.AEAD
	enabled      bool
	// The key version to use for new encryptions
	currentKeyVersion string
}

// NewAESEncryptionService creates a new AES encryption service.
// It expects a map of key versions to their base64-encoded key strings.
// The `currentKeyVersion` is used for all new encryptions.
func NewAESEncryptionService(
	ctx context.Context,
	keys map[string]string,
	currentKeyVersion string,
) (*AESEncryptionService, error) {
	if len(keys) == 0 {
		slog.WarnContext(ctx, "No encryption keys provided; encryption is disabled.")
		return &AESEncryptionService{enabled: false}, nil
	}

	if _, ok := keys[currentKeyVersion]; !ok {
		return nil, fmt.Errorf(
			"current key version '%s' not found in provided keys",
			currentKeyVersion,
		)
	}

	gcmInstances := make(map[string]cipher.AEAD)
	for version, keyStr := range keys {
		key, err := base64.StdEncoding.DecodeString(keyStr)
		if err != nil {
			return nil, fmt.Errorf("invalid base64 key for version '%s': %w", version, err)
		}
		if len(key) != 32 {
			return nil, fmt.Errorf("key for version '%s' must be 32 bytes, got %d", version, len(key))
		}

		block, err := aes.NewCipher(key)
		if err != nil {
			return nil, fmt.Errorf("failed to create AES cipher for version '%s': %w", version, err)
		}

		gcm, err := cipher.NewGCM(block)
		if err != nil {
			return nil, fmt.Errorf("failed to create GCM for version '%s': %w", version, err)
		}
		gcmInstances[version] = gcm
	}

	slog.InfoContext(ctx, "AES Encryption Service enabled", "current_version", currentKeyVersion, "total_keys", len(gcmInstances))
	return &AESEncryptionService{
		gcmInstances:      gcmInstances,
		enabled:           true,
		currentKeyVersion: currentKeyVersion,
	}, nil
}

// EncryptField encrypts a string value using AES-GCM.
func (a *AESEncryptionService) EncryptField(value string) (string, error) {
	if !a.enabled || value == "" {
		return value, nil
	}

	gcm := a.gcmInstances[a.currentKeyVersion]

	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Encrypt the data. AAD is set to nil.
	ciphertext := gcm.Seal(nonce, nonce, []byte(value), nil)

	// Format: enc:v1:<base64(nonce+ciphertext)>
	encoded := base64.StdEncoding.EncodeToString(ciphertext)
	return strings.Join([]string{encryptionPrefix, a.currentKeyVersion, encoded}, separator), nil
}

// DecryptField decrypts a string value using AES-GCM.
func (a *AESEncryptionService) DecryptField(encryptedValue string) (string, error) {
	if !a.enabled || encryptedValue == "" {
		return encryptedValue, nil
	}

	parts := strings.SplitN(encryptedValue, separator, 3)
	if len(parts) != 3 || parts[0] != encryptionPrefix {
		// Not in our encrypted format, return as-is for backward compatibility.
		// This replaces the fragile `isLikelyEncrypted` check.
		return encryptedValue, nil
	}

	version := parts[1]
	payload := parts[2]

	gcm, ok := a.gcmInstances[version]
	if !ok {
		return "", fmt.Errorf("unknown encryption key version: %s", version)
	}

	ciphertext, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted value: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	// Decrypt the data. AAD is set to nil.
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt value (data may be corrupt or key is wrong): %w", err)
	}

	return string(plaintext), nil
}

// EncryptEmail is a helper for encrypting email addresses.
func (a *AESEncryptionService) EncryptEmail(email *string) (*string, error) {
	if email == nil || *email == "" {
		return email, nil
	}
	encrypted, err := a.EncryptField(*email)
	if err != nil {
		return nil, err
	}
	return &encrypted, nil
}

// DecryptEmail is a helper for decrypting email addresses.
func (a *AESEncryptionService) DecryptEmail(encryptedEmail *string) (*string, error) {
	if encryptedEmail == nil || *encryptedEmail == "" {
		return encryptedEmail, nil
	}
	decrypted, err := a.DecryptField(*encryptedEmail)
	if err != nil {
		return nil, err
	}
	return &decrypted, nil
}

// IsEnabled returns whether encryption is enabled.
func (a *AESEncryptionService) IsEnabled() bool {
	return a.enabled
}
