global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  # Mail Alias Management API
  - job_name: 'mail-alias-api'
    static_configs:
      - targets: ['mail-alias-management:5000']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Optional: If you enable Redis later
  # - job_name: 'redis'
  #   static_configs:
  #     - targets: ['redis:6379']
  #   metrics_path: /metrics

alerting:
  alertmanagers:
    - static_configs:
        - targets: []