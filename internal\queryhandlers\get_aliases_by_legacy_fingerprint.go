package queryhandlers

import (
	"context"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/queries"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
)

type GetAliasesByLegacyFingerprintQueryHandler struct {
	aliasRepo repository.AliasRepository
}

func NewGetAliasesByLegacyFingerprintQueryHandler(aliasRepo repository.AliasRepository) *GetAliasesByLegacyFingerprintQueryHandler {
	return &GetAliasesByLegacyFingerprintQueryHandler{aliasRepo: aliasRepo}
}

func (h *GetAliasesByLegacyFingerprintQueryHandler) Handle(ctx context.Context, query *queries.GetAliasesByLegacyFingerprintQuery) ([]*core.Alias, error) {
	aliases, err := h.aliasRepo.GetAliasesByLegacyFingerprint(ctx, query.Fingerprint, query.Limit, query.Offset)
	if err != nil {
		return nil, err
	}

	aliases = core.FilterActiveAndNotExpiredAliases(aliases)

	if len(aliases) == 0 {
		return nil, core.ErrAliasNotFound
	}

	return aliases, nil
}
