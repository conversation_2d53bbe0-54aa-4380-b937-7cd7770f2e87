package repository

import (
	"context"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
)

// AliasRepository defines the interface for alias data access operations.
type AliasRepository interface {
	// Commands (Write operations)
	SaveAlias(ctx context.Context, alias *core.Alias) error
	UpdateAliasSenderEmail(ctx context.Context, alias *core.Alias, senderEmail string, maskedSenderEmail string) (*core.Alias, error)

	// Queries (Read operations)
	GetAliasByID(ctx context.Context, aliasID string) (*core.Alias, error)
	GetAliasByLocatorAndPurpose(ctx context.Context, locator, purpose string) (*core.Alias, error)
	GetAliasesByGuestUserID(ctx context.Context, guestUserID string, limit, offset int) ([]*core.Alias, error)
	GetAliasesByHostUserID(ctx context.Context, hostUserID string, limit, offset int) ([]*core.Alias, error)
	GetAliasByContextFingerprint(ctx context.Context, fingerprint string) (*core.Alias, error) // Returns single, as fingerprint implies uniqueness
	GetAliasesByLegacyFingerprint(ctx context.Context, fingerprint string, limit, offset int) ([]*core.Alias, error)
	//GetAliasesByOriginalRecipientEmail(ctx context.Context, email, purpose string, limit, offset int) ([]*core.Alias, error)
}
