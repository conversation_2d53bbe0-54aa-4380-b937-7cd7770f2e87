package main

import "time"

// Current table structure for email tokens
type EmailTokenCommand struct {
	ID            int       `json:"id"`
	OriginalEmail string    `json:"original_email"`
	Mask          string    `json:"mask"`
	Token         string    `json:"token"`
	Fingerprint   string    `json:"fingerprint"`
	DateCreated   time.Time `json:"date_created"`
	ModeCreation  int       `json:"mode_creation"`
	Active        bool      `json:"active"`
}

// Current table structure for token locators
type TokenLocatorCommand struct {
	ID          int    `json:"id"`
	Token       string `json:"token"`
	Locator     int    `json:"locator"`
	Channel     int    `json:"channel"`
	ServiceDate string `json:"service_date"`
}

type EmailTokenWithLocator struct {
	OriginalEmail string    `json:"original_email"`
	Mask          string    `json:"mask"`
	Token         string    `json:"token"`
	Fingerprint   string    `json:"fingerprint"`
	DateCreated   time.Time `json:"date_created"`
	ModeCreation  int       `json:"mode_creation"`
	Active        bool      `json:"active"`
	Locator       int       `json:"locator"`
	Channel       int       `json:"channel"`
	ServiceDate   string    `json:"service_date"`
}
