package queries

// GetAliasByIDQuery represents a request to retrieve a single alias by its unique ID.
// This is the primary lookup used by the MPRS.
type GetAliasByIDQuery struct {
	AliasID string `json:"alias_id" validate:"required,email"` // Use "email" validator if alias_id strictly follows email format
}

// GetAliasesByLocatorContextQuery represents a request to retrieve aliases
// associated with a specific locator (e.g., Booking Locator), optionally filtered by purpose.
// This replaces the previous GetAliasesByBookingContextQuery.
type GetAliasByLocatorAndPurposeQuery struct {
	Locator string `json:"locator" validate:"required,alphanum"`
	Purpose string `json:"purpose" validate:"required"`
}

// GetAliasesByGuestUserIDQuery represents a request to retrieve all aliases
// associated with a specific guest user. Crucial for Privacy compliance.
type GetAliasesByGuestUserIDQuery struct {
	GuestUserID string `json:"guest_user_id" validate:"required,uuid"`   // Assuming UserID is a UUID
	Limit       int    `json:"limit" validate:"omitempty,min=1,max=100"` // Optional: for pagination
	Offset      int    `json:"offset" validate:"omitempty,min=0"`        // Optional: for pagination
}

// GetAliasesByHostUserIDQuery represents a request to retrieve all aliases
// associated with a specific host/hotel.
type GetAliasesByHostUserIDQuery struct {
	HostUserID string `json:"host_user_id" validate:"required,uuid"` // Assuming HostUserID is a UUID
	Limit      int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Offset     int    `json:"offset" validate:"omitempty,min=0"`
}

// GetAliasByContextFingerprintQuery represents a request to retrieve an alias
// by its computed context fingerprint. Used by the AMS itself to check for existing aliases.
type GetAliasByContextFingerprintQuery struct {
	ContextFingerprint string `json:"context_fingerprint" validate:"required,alphanum"` // Assuming hash is alphanumeric
}

// GetAliasesByLegacyFingerprintQuery represents a request to find aliases using the legacy fingerprint.
// This fingerprint is typically a hash of only the original_recipient_email
type GetAliasesByLegacyFingerprintQuery struct {
	Fingerprint string `json:"fingerprint" validate:"required,alphanum"` // The legacy fingerprint hash
	Limit       int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Offset      int    `json:"offset" validate:"omitempty,min=0"`
}

// GetAliasesByOriginalRecipientEmailQuery represents a request to find aliases
type GetAliasesByOriginalRecipientEmailQuery struct {
	OriginalRecipientEmail string `json:"original_recipient_email" validate:"required,email"`
	Purpose                string `json:"purpose" validate:"omitempty,alpha_dash"`
	Limit                  int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Offset                 int    `json:"offset" validate:"omitempty,min=0"`
}
