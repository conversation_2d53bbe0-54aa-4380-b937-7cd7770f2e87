package queryhandlers

import (
	"context"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/queries"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
)

type GetAliasesByHostUserIDQueryHandler struct {
	aliasRepo repository.AliasRepository
}

func NewGetAliasesByHostUserIDQueryHandler(aliasRepo repository.AliasRepository) *GetAliasesByHostUserIDQueryHandler {
	return &GetAliasesByHostUserIDQueryHandler{aliasRepo: aliasRepo}
}

func (h *GetAliasesByHostUserIDQueryHandler) Handle(ctx context.Context, query *queries.GetAliasesByHostUserIDQuery) ([]*core.<PERSON>as, error) {
	aliases, err := h.aliasRepo.GetAliasesByHostUserID(ctx, query.HostUserID, query.Limit, query.Offset)
	if err != nil {
		return nil, err
	}

	aliases = core.FilterActiveAndNotExpiredAliases(aliases)

	if len(aliases) == 0 {
		return nil, core.ErrAliasNotFound
	}

	return aliases, nil
}
