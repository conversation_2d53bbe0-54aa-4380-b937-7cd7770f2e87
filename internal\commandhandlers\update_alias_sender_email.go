package commandhandlers

import (
	"context"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/commands"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core/masking"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
)

// TODO: this function needs to be tested properly and handled in more secure why rather than direct aproach

type UpdateAliasSenderEmailCommandHandler struct {
	aliasRepo   repository.AliasRepository
	emailMasker *masking.EmailMasker
}

func NewUpdateAliasSenderEmailCommandHandler(aliasRepository repository.AliasRepository, emailMasker *masking.EmailMasker) *UpdateAliasSenderEmailCommandHandler {
	return &UpdateAliasSenderEmailCommandHandler{
		aliasRepo:   aliasRepository,
		emailMasker: emailMasker,
	}
}

func (h *UpdateAliasSenderEmailCommandHandler) Handle(ctx context.Context, cmd *commands.UpdateAliasSenderEmailCommand) (*core.Alias, error) {
	maskedSenderEmail := h.emailMasker.MaskEmail(cmd.SenderEmail)

	alias, err := h.aliasRepo.GetAliasByID(ctx, cmd.AliasID)
	if err != nil {
		return nil, err
	}

	if alias.OriginalRecipientEmail == cmd.SenderEmail {
		return nil, core.ErrSenderIsSameAsRecipient
	}

	updatedAlias, err := h.aliasRepo.UpdateAliasSenderEmail(ctx, alias, cmd.SenderEmail, maskedSenderEmail)
	if err != nil {
		return nil, err
	}

	return updatedAlias, nil
}
