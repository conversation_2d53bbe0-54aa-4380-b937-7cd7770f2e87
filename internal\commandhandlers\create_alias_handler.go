package commandhandlers

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/commands"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core/aliasgenerator"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core/fingerprint"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core/masking"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
)

type CreateAliasCommandHandler struct {
	aliasRepo            repository.AliasRepository
	aliasGenerator       *aliasgenerator.AliasGenerator
	emailMasker          *masking.EmailMasker
	fingerprintGenerator *fingerprint.FingerprintGenerator
}

func NewCreateAliasCommandHandler(aliasRepository repository.AliasRepository, aliasGenerator *aliasgenerator.AliasGenerator, emailMasker *masking.EmailMasker, fingerprintGenerator *fingerprint.FingerprintGenerator) *CreateAliasCommandHandler {
	return &CreateAliasCommandHandler{
		aliasRepo:            aliasRepository,
		aliasGenerator:       aliasGenerator,
		emailMasker:          emailMasker,
		fingerprintGenerator: fingerprintGenerator,
	}
}

func (h *CreateAliasCommandHandler) Handle(ctx context.Context, cmd *commands.CreateAliasCommand) (*core.Alias, error) {
	// Generate legacy fingerprint for backwards compatibility
	fingerprint, err := h.fingerprintGenerator.GenerateLegacyFingerprint(cmd.OriginalRecipientEmail)
	if err != nil {
		return nil, fmt.Errorf("error generating legacy fingerprint: %w", err)
	}

	// Generate context fingerprint for alias uniqueness
	contextFingerprint, err := h.fingerprintGenerator.GenerateContextFingerprint(cmd.OriginalSenderEmail, cmd.OriginalRecipientEmail, cmd.Purpose, cmd.Locator)
	if err != nil {
		return nil, fmt.Errorf("error generating fingerprint: %w", err)
	}

	// Checks if alias already exists using contextfingerprint if exists return it else continue with creation
	existingAlias, err := h.aliasRepo.GetAliasByContextFingerprint(ctx, contextFingerprint)
	if err != nil {
		if err != core.ErrAliasNotFound {
			return nil, fmt.Errorf("error getting alias by context fingerprint: %w", err)
		}
	}

	if existingAlias != nil {
		if existingAlias.Status == "active" && existingAlias.ExpiresAt.After(time.Now()) {
			return existingAlias, nil
		}
	}

	aliasID, err := h.aliasGenerator.GenerateAliasID(cmd.Purpose, cmd.Locator)
	if err != nil {
		return nil, fmt.Errorf("error generating alias ID: %w", err)
	}

	maskedRecipientEmail := h.emailMasker.MaskEmail(cmd.OriginalRecipientEmail)

	var maskedSenderEmail string
	if cmd.OriginalSenderEmail != nil {
		maskedSenderEmail = h.emailMasker.MaskEmail(*cmd.OriginalSenderEmail)
	}

	communicationDirection := cmd.CommunicationDirection
	if communicationDirection == "" {
		communicationDirection, err = core.GetCommunicationDirectionForPurpose(cmd.Purpose)
		if err != nil {
			return nil, fmt.Errorf("error getting communication direction for purpose: %w", err)
		}
	}

	Alias := core.Alias{
		AliasID:                aliasID,
		OriginalRecipientEmail: cmd.OriginalRecipientEmail,
		OriginalSenderEmail:    cmd.OriginalSenderEmail,
		MaskedRecipientEmail:   maskedRecipientEmail,
		MaskedSenderEmail:      &maskedSenderEmail,
		Locator:                hashLocator(cmd.Locator),
		Fingerprint:            fingerprint,
		ContextFingerprint:     contextFingerprint,
		GuestUserID:            cmd.GuestUserID,
		HostUserID:             cmd.HostUserID,
		Purpose:                cmd.Purpose,
		Channel:                cmd.Channel,
		CommunicationDirection: communicationDirection,
		CreatorService:         cmd.CreatorService,
		CreatedAt:              time.Now().UTC(),
		ExpiresAt:              cmd.ExpiresAt,
		Status:                 "active",
		ProductType:            cmd.ProductType,
		LastUsedAt:             nil,
	}

	if err := h.aliasRepo.SaveAlias(ctx, &Alias); err != nil {
		return nil, err
	}

	return &Alias, nil
}

func hashLocator(locator string) string {
	hash := sha256.New()
	hash.Write([]byte(locator))
	return hex.EncodeToString(hash.Sum(nil))
}
