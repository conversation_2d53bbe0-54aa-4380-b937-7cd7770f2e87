package logger

import (
	"io"
	"os"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// Config holds logger configuration
type Config struct {
	Level       string `json:"level"`
	Environment string `json:"environment"`
	ServiceName string `json:"service_name"`
	Pretty      bool   `json:"pretty"`
}

// New creates a new logger instance
func New(config Config) *zerolog.Logger {
	// Set global log level
	level, err := zerolog.ParseLevel(config.Level)
	if err != nil {
		log.Warn().Msgf("Invalid log level '%s', defaulting to 'info'", config.Level)
		level = zerolog.InfoLevel
	}

	// Configure output
	var output io.Writer = os.Stdout

	// Use pretty logging for development
	if config.Pretty || config.Environment == "development" {
		output = zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: time.RFC3339,
			NoColor:    false,
		}
	}

	// Create logger with context
	logger := zerolog.New(output).
		Level(level).
		With().
		Timestamp().
		Str("service", config.ServiceName).
		Str("environment", config.Environment).
		Logger()

	// TODO: can be returned as pure value for read only
	return &logger
}

// NewDefault creates a logger with default configuration
func NewDefault(serviceName string) *zerolog.Logger {
	return New(Config{
		Level:       "info",
		Environment: "development",
		ServiceName: serviceName,
		Pretty:      true,
	})
}
