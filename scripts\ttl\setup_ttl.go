package main

import (
	"context"
	"flag"
	"fmt"
	"log"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/infrastructure/config"
	dynamodbclient "a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/infrastructure/database/dynamodb"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

func main() {
	var (
		tableName = flag.String("table", "", "DynamoDB table name")
		region    = flag.String("region", "us-east-1", "AWS region")
		disable   = flag.Bool("disable", false, "Disable TTL instead of enabling it")
	)
	flag.Parse()

	if *tableName == "" {
		log.Fatal("Table name is required. Use -table flag.")
	}

	ctx := context.Background()

	// Create DynamoDB config
	dynamoConfig := &config.DynamoDBConfig{
		Region: *region,
		Table:  *tableName,
	}

	// Create DynamoDB client
	client, err := dynamodbclient.NewDynamoDBClient(ctx, dynamoConfig)
	if err != nil {
		log.Fatalf("Failed to create DynamoDB client: %v", err)
	}

	// Enable or disable TTL
	action := "enabling"
	ttlEnabled := true
	if *disable {
		action = "disabling"
		ttlEnabled = false
	}

	fmt.Printf("Setting up TTL for table %s in region %s...\n", *tableName, *region)
	fmt.Printf("Action: %s TTL\n", action)

	// Update TTL specification
	ttlSpec := &types.TimeToLiveSpecification{
		AttributeName: aws.String("ttl"),
		Enabled:       aws.Bool(ttlEnabled),
	}

	updateTTLInput := &dynamodb.UpdateTimeToLiveInput{
		TableName:               aws.String(*tableName),
		TimeToLiveSpecification: ttlSpec,
	}

	_, err = client.UpdateTimeToLive(ctx, updateTTLInput)
	if err != nil {
		log.Fatalf("Failed to update TTL: %v", err)
	}

	fmt.Printf("Successfully %s TTL for table %s\n", action, *tableName)
	fmt.Println("\nTTL Configuration:")
	fmt.Printf("- Attribute Name: ttl\n")
	fmt.Printf("- Enabled: %t\n", ttlEnabled)
	fmt.Println("\nNote: TTL deletion happens within 48 hours of expiration (AWS SLA)")
	fmt.Println("Items will be automatically removed from all indexes when TTL expires.")
}
