package fingerprint

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"strings"

	"golang.org/x/text/encoding/unicode"
)

type FingerprintGenerator struct{}

func NewFingerprintGenerator() *FingerprintGenerator {
	return &FingerprintGenerator{}
}

func (f *FingerprintGenerator) GenerateLegacyFingerprint(originalRecipientEmail string) (string, error) {
	originalRecipientEmail = strings.ToLower(originalRecipientEmail)
	utf16Email, err := f.convertUTF8ToUTF16LE(originalRecipientEmail)
	if err != nil {
		return "", fmt.Errorf("error converting utf8 to utf16le: %w", err)
	}
	data := []byte(utf16Email)

	fingerprint := f.bytesToHash256SumBase64(data)

	return fingerprint, nil
}

// GenerateContextFingerprint creates a unique hash for a given alias context.
// This hash is used to prevent duplicate alias creation for the same logical communication channel.
//
// Parameters should ideally be the raw values from the CreateAliasCommand.
// The function internally handles nil/empty strings based on purpose.
func (f *FingerprintGenerator) GenerateContextFingerprint(
	originalSenderEmail *string, // Use pointer type as it can be nil
	originalRecipientEmail, locator, purpose string,
) (string, error) {
	sender := ""
	if originalSenderEmail != nil {
		sender = strings.ToLower(*originalSenderEmail)
	}
	recipient := strings.ToLower(originalRecipientEmail)
	loc := strings.ToLower(locator)
	purp := strings.ToLower(purpose)

	// The fingerprint will always be based on these 4 components.
	// If sender is null/empty, its contribution will consistently be an empty string.
	combined := fmt.Sprintf("%s|%s|%s|%s", sender, recipient, loc, purp)

	// Calculate SHA256 hash
	hasher := sha256.New()
	hasher.Write([]byte(combined))
	hashBytes := hasher.Sum(nil)

	// Encode to Base64 string
	return base64.URLEncoding.EncodeToString(hashBytes), nil
}

func (f *FingerprintGenerator) convertUTF8ToUTF16LE(message string) (string, error) {
	utf16le := unicode.UTF16(unicode.LittleEndian, unicode.IgnoreBOM)
	utfEncoder := utf16le.NewEncoder()
	ut16LeEncodedMessage, err := utfEncoder.String(message)

	if err != nil {
		return "", fmt.Errorf("error converting utf8 to utf16le: %w", err)
	}

	return ut16LeEncodedMessage, nil
}

func (f *FingerprintGenerator) bytesToHash256SumBase64(bytes []byte) string {
	encoded := ""
	hash := sha256.Sum256(bytes)
	encoded = base64.StdEncoding.EncodeToString(hash[:])
	return encoded
}
