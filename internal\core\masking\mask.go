package masking

import (
	"fmt"
	"strings"
)

// EmailMasker provides methods for robust email masking.
type EmailMasker struct{}

// NewEmailMasker creates a new instance of EmailMasker.
func NewEmailMasker() *EmailMasker {
	return &EmailMasker{}
}

// MaskEmail masks an email address while preserving the first and last characters of the local part,
// and the domain.
func (m *EmailMasker) MaskEmail(email string) string {
	if email == "" {
		return ""
	}

	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return email // Not a valid email format
	}

	localPart := parts[0]
	domain := parts[1]

	maskedLocalPart := m.maskLocalPart(localPart)

	return fmt.Sprintf("%s@%s", maskedLocalPart, domain)
}

// maskLocalPart applies masking logic to the local part of the email.
// It masks each segment separated by dots.
func (m *EmailMasker) maskLocalPart(localPart string) string {
	runes := []rune(localPart) // Convert to runes to handle multi-byte UTF-8 characters correctly
	length := len(runes)

	// Don't mask if too short (e.g., "a", "ab")
	if length <= 2 {
		return localPart
	}

	// For local parts with dots, mask each segment
	if strings.Contains(localPart, ".") {
		segments := strings.Split(localPart, ".")
		maskedSegments := make([]string, len(segments))
		for i, segment := range segments {
			maskedSegments[i] = maskSimpleString(segment)
		}
		return strings.Join(maskedSegments, ".")
	}

	// For simple local parts without dots (e.g., 'username')
	return maskSimpleString(localPart)
}

// maskSimpleString masks a string by preserving the first and last runes, replacing middle with asterisks.
// Handles UTF-8 characters correctly.
func maskSimpleString(s string) string {
	runes := []rune(s)
	length := len(runes)

	if length <= 2 {
		return s // Cannot effectively mask 1 or 2 characters
	}

	firstChar := runes[0]
	lastChar := runes[length-1]
	maskLength := length - 2

	var b strings.Builder
	b.WriteRune(firstChar)
	for i := 0; i < maskLength; i++ {
		b.WriteRune('*')
	}
	b.WriteRune(lastChar)
	return b.String()
}
