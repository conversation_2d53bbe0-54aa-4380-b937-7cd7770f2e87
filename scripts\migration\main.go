package main

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core/fingerprint"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/infrastructure/config"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/infrastructure/database/dynamodb"
	"github.com/rs/zerolog"
)

func main() {

	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()

	dbManager, err := connectDB(&logger)
	if err != nil {
		logger.Fatal().Err(err).Msg("Failed to connect to database")
	}

	defer dbManager.Close()

	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()

	// In your main.go or wherever you want to use it
	migrator := NewMigrator(dbManager.GetPool(), &logger, "public", "token_locator", "email_token")

	count, err := migrator.GetEmailTokensCount(ctx)
	if err != nil {
		logger.Fatal().Err(err).Msg("Failed to get email tokens count")
	}

	fmt.Println("Total email tokens:", count)

	var limit int = 100
	var offset int = 0

	// Init DynamoDB client
	dynamoClient, err := dynamodb.NewDynamoDBClient(ctx, &config.DynamoDBConfig{
		Region:         "us-east-1",
		Table:          "EmailAliases",
		CustomEndpoint: "",
	})
	if err != nil {
		log.Fatalf("Failed to create DynamoDB client: %v", err)
	}

	// Create AES encryption service (disabled for migration)
	aesEncryption, err := dynamodb.NewAESEncryptionService(ctx, map[string]string{
		"v1": "8SXP3234O+QU3QWxNEQWvlRRkSVAlbMNOtucP5TEj2w=",
	}, "v1")
	if err != nil {
		log.Fatalf("Failed to create AES encryption service: %v", err)
	}

	aliasRepository := dynamodb.NewDynamoDBAliasRepository(dynamoClient, "EmailAliases", true, 90, aesEncryption)

	fingerprintGenerator := fingerprint.NewFingerprintGenerator()

	var purpose string = "migration"
	var status string = "active"
	var communicationDirection string = "bidirectional"
	var creatorService string = "migrator-script"

	for {
		emailTokens, err := migrator.GetEmailTokensPaginated(ctx, limit, offset)
		if err != nil {
			logger.Fatal().Err(err).Msg("Failed to retrieve email tokens")
		}

		if len(emailTokens) == 0 {
			break
		}

		for _, emailToken := range emailTokens {
			locator := strconv.Itoa(emailToken.Locator)

			contextFingerprint, err := fingerprintGenerator.GenerateContextFingerprint(
				nil,
				emailToken.OriginalEmail,
				locator,
				purpose,
			)

			if err != nil {
				logger.Fatal().Err(err).Msg("Failed to generate context fingerprint")
			}

			alias := &core.Alias{
				AliasID:                emailToken.Token,
				OriginalRecipientEmail: emailToken.OriginalEmail,
				MaskedRecipientEmail:   emailToken.Mask,
				Fingerprint:            emailToken.Fingerprint,
				ContextFingerprint:     contextFingerprint,
				Locator:                hashLocator(locator),
				Channel:                &emailToken.Channel,
				Status:                 status,
				CreatedAt:              emailToken.DateCreated,
				CommunicationDirection: communicationDirection,
				CreatorService:         creatorService,
				Purpose:                purpose,
				ExpiresAt:              emailToken.DateCreated.AddDate(1, 0, 0),
			}

			existingAlias, err := aliasRepository.GetAliasByID(ctx, alias.AliasID)
			_ = err
			if existingAlias != nil {
				continue
			}

			err = aliasRepository.SaveAlias(ctx, alias)
			if err != nil {
				logger.Fatal().Err(err).Msg("Failed to save alias")
			}

		}

		offset += limit
	}

}

func connectDB(log *zerolog.Logger) (*Manager, error) {
	// Convert config to database config
	dbConfig := &Config{
		Host:            "host",
		Port:            5432,
		Database:        "db",
		Username:        "user",
		Password:        "psw",
		SSLMode:         "disable",
		MaxConns:        25,
		MinConns:        5,
		MaxConnLifetime: time.Hour,
		MaxConnIdleTime: time.Minute * 30,
		ConnectTimeout:  time.Second * 30,
	}

	// Create database manager
	dbManager := NewManager(dbConfig, log)

	// Connect to database with context
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()

	if err := dbManager.Connect(ctx); err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return dbManager, nil
}

func hashLocator(locator string) string {
	hash := sha256.New()
	hash.Write([]byte(locator))
	return hex.EncodeToString(hash.Sum(nil))
}
