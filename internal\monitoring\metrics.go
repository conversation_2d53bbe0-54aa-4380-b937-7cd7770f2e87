package monitoring

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	HttpRequestsTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "http_requests_total",
		Help: "Total number of HTTP requests.",
	}, []string{"method", "path", "status"})

	HttpRequestDurationMs = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "http_request_duration_ms",
		Help:    "Duration of HTTP requests in milliseconds.",
		Buckets: []float64{10, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 30000}, // Millisecond buckets
	}, []string{"method", "path", "status"})

	// === Custom Application-Specific Metrics ===

	// AliasCreationAttemptsTotal is a counter for alias creation attempts (API calls).
	// Example usage: monitoring.AliasCreationAttemptsTotal.Inc()
	AliasCreationAttemptsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "alias_creation_attempts_total",
		Help: "Total number of alias creation API call attempts.",
	})

	// AliasCreationFailuresTotal is a counter for failed alias creations, labeled by reason.
	// Example usage: monitoring.AliasCreationFailuresTotal.WithLabelValues("validation_error").Inc()
	AliasCreationFailuresTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "alias_creation_failures_total",
		Help: "Total number of failed alias creations.",
	}, []string{"reason"}) // e.g., "validation_error", "db_error", "conflict", "internal_error"

	// AliasLookupTotal is a counter for all alias lookup attempts (e.g., GetAliasByID).
	// Example usage: monitoring.AliasLookupTotal.Inc()
	AliasLookupTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "alias_lookup_total",
		Help: "Total number of alias lookup attempts.",
	})

	// AliasLookupErrorsTotal is a counter for failed alias lookups, labeled by reason.
	// Example usage: monitoring.AliasLookupErrorsTotal.WithLabelValues("not_found").Inc()
	AliasLookupErrorsTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "alias_lookup_errors_total",
		Help: "Total number of failed alias lookups.",
	}, []string{"reason", "alias_id"}) // e.g., "not_found", "db_error", "internal_error"

	AliasUpdateAttemptsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "alias_update_attempts_total",
		Help: "Total number of alias update attempts.",
	})

	AliasUpdateFailuresTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "alias_update_failures_total",
		Help: "Total number of failed alias updates.",
	}, []string{"reason"}) // e.g., "validation_error", "db_error", "internal_error"
)

func InitMetrics() {}
