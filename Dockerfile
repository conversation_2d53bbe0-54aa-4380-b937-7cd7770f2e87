# Build stage
FROM golang:1.24.3-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata && \
    rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy go mod files first for better caching
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code
COPY cmd/ cmd/
COPY internal/ internal/
COPY pkg/ pkg/

# Build the application with optimized flags for production
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -trimpath \
    -o main cmd/server/main.go

# Verify the binary
RUN ./main --version 2>/dev/null || echo "Binary built successfully"

# Production stage
FROM alpine:latest

# Install runtime dependencies and security updates
RUN apk update && \
    apk add --no-cache \
        ca-certificates \
        wget \
        tzdata \
        dumb-init && \
    rm -rf /var/cache/apk/* && \
    update-ca-certificates

# Create a non-root user for security
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup -s /bin/sh

# Set production environment variables
ENV PORT=5000 \
    HEALTH_PORT=5000

# Create necessary directories with proper permissions
RUN mkdir -p /app/configs /app/logs && \
    chown -R appuser:appgroup /app

# Copy timezone data from builder
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

# Copy the binary with proper permissions
COPY --from=builder /app/main /app/main
RUN chmod +x /app/main && \
    chown appuser:appgroup /app/main

# Copy configuration files
COPY --chown=appuser:appgroup configs/ /app/configs/

# Switch to non-root user
USER appuser

# Set working directory
WORKDIR /app

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:${HEALTH_PORT}/health || exit 1

# Expose application and health check ports
EXPOSE ${PORT}
EXPOSE ${HEALTH_PORT}

# Add labels for better container management
LABEL maintainer="<EMAIL>" \
      version="1.0" \
      description="Mail Alias Management System" \
      app="mail-alias-management"

# Use dumb-init to handle signals properly
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# Run the application
CMD ["/app/main"]