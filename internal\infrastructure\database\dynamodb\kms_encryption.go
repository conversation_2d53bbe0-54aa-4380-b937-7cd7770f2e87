package dynamodb

import (
	"context"
	"encoding/base64"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/kms"
)

// KMSEncryptionService handles encryption and decryption of sensitive data using AWS KMS
type KMSEncryptionService struct {
	kmsClient *kms.Client
	keyID     string
	enabled   bool
}

// NewKMSEncryptionService creates a new KMS encryption service
func NewKMSEncryptionService(cfg aws.Config, keyID string, enabled bool) *KMSEncryptionService {
	var kmsClient *kms.Client
	if enabled {
		kmsClient = kms.NewFromConfig(cfg)
	}

	return &KMSEncryptionService{
		kmsClient: kmsClient,
		keyID:     keyID,
		enabled:   enabled,
	}
}

// EncryptField encrypts a string value using KMS
func (k *KMSEncryptionService) EncryptField(ctx context.Context, value string) (string, error) {
	if !k.enabled || value == "" {
		return value, nil
	}

	input := &kms.EncryptInput{
		KeyId:     aws.String(k.keyID),
		Plaintext: []byte(value),
	}

	result, err := k.kmsClient.Encrypt(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt field: %w", err)
	}

	// Encode the encrypted data as base64 for storage
	return base64.StdEncoding.EncodeToString(result.CiphertextBlob), nil
}

// DecryptField decrypts a string value using KMS
func (k *KMSEncryptionService) DecryptField(ctx context.Context, encryptedValue string) (string, error) {
	if !k.enabled || encryptedValue == "" {
		return encryptedValue, nil
	}

	// Decode the base64 encrypted data
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedValue)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted field: %w", err)
	}

	input := &kms.DecryptInput{
		CiphertextBlob: ciphertext,
	}

	result, err := k.kmsClient.Decrypt(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt field: %w", err)
	}

	return string(result.Plaintext), nil
}

// EncryptEmail encrypts an email address (handles both string and *string)
func (k *KMSEncryptionService) EncryptEmail(ctx context.Context, email *string) (*string, error) {
	if email == nil || *email == "" {
		return email, nil
	}

	encrypted, err := k.EncryptField(ctx, *email)
	if err != nil {
		return nil, err
	}

	return &encrypted, nil
}

// DecryptEmail decrypts an email address (handles both string and *string)
func (k *KMSEncryptionService) DecryptEmail(ctx context.Context, encryptedEmail *string) (*string, error) {
	if encryptedEmail == nil || *encryptedEmail == "" {
		return encryptedEmail, nil
	}

	decrypted, err := k.DecryptField(ctx, *encryptedEmail)
	if err != nil {
		return nil, err
	}

	return &decrypted, nil
}
