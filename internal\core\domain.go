package core

import (
	"time"
)

type Alias struct {
	AliasID                string     `json:"alias_id" dynamodbav:"alias_id"`
	OriginalRecipientEmail string     `json:"original_recipient_email" dynamodbav:"original_recipient_email"`
	OriginalSenderEmail    *string    `json:"original_sender_email" dynamodbav:"original_sender_email,omitempty"`
	MaskedRecipientEmail   string     `json:"masked_recipient_email" dynamodbav:"masked_recipient_email"`
	MaskedSenderEmail      *string    `json:"masked_sender_email" dynamodbav:"masked_sender_email,omitempty"`
	Locator                string     `json:"locator" dynamodbav:"locator"`
	GuestUserID            *string    `json:"guest_user_id" dynamodbav:"guest_user_id,omitempty"`
	HostUserID             *string    `json:"host_user_id" dynamodbav:"host_user_id,omitempty"`
	Fingerprint            string     `json:"fingerprint" dynamodbav:"fingerprint,omitempty"`
	ContextFingerprint     string     `json:"context_fingerprint" dynamodbav:"context_fingerprint"`
	Purpose                string     `json:"purpose" dynamodbav:"purpose"`
	Channel                *int       `json:"channel" dynamodbav:"channel,omitempty"`
	CommunicationDirection string     `json:"communication_direction" dynamodbav:"communication_direction"`
	CreatorService         string     `json:"creator_service" dynamodbav:"creator_service,omitempty"`
	CreatedAt              time.Time  `json:"created_at" dynamodbav:"created_at"`
	ExpiresAt              time.Time  `json:"expires_at" dynamodbav:"expires_at"`
	Status                 string     `json:"status" dynamodbav:"status"`
	LastUsedAt             *time.Time `json:"last_used_at" dynamodbav:"last_used_at,omitempty"`
	ProductType            *string    `json:"product_type" dynamodbav:"product_type,omitempty"`
}
