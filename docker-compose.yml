services:
  mail-alias-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mail-alias-management
    ports:
      - "5000:5000"
      #- "10101:10101" # Monitoring port
    environment:
      # Override config values via environment variables if needed
      - SERVER_PORT=5000
      - SERVER_DEBUG=true
      - SERVER_BUILD=production
      - AUTH_ENABLED=true
      - RATELIMIT_ENABLED=true
      - MONITORING_ENABLED=true
      # AWS Configuration
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - AWS_PROFILE=${AWS_PROFILE:-default}
    volumes:
      # Mount config directory for runtime configuration changes
      - ./configs:/configs:ro
      # Mount AWS credentials from host machine
      - ~/.aws:/home/<USER>/.aws:ro
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 256M
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget --no-verbose --tries=1 --spider http://localhost:5000/heartbeat || exit 1",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - mail-alias-network

      # Prometheus: Metrics collection and storage
  prometheus:
    image: prom/prometheus:latest
    container_name: mail-alias-prometheus
    ports:
      - "9090:9090"
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=30d"
      - "--web.enable-lifecycle"
      - "--web.external-url=http://localhost:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget --no-verbose --tries=1 --spider http://localhost:9090/-/healthy || exit 1",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - mail-alias-network
    depends_on:
      - mail-alias-api

    # Grafana: Metrics visualization and dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: mail-alias-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "0.3"
          memory: 512M
        reservations:
          cpus: "0.1"
          memory: 256M
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - mail-alias-network
    depends_on:
      - prometheus
  # Redis: In-memory data store for caching and session management
  redis:
    image: redis:7-alpine
    container_name: mail-alias-redis
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    command: >
      sh -c "
      if [ -n \"$$REDIS_PASSWORD\" ]; then
        redis-server --requirepass $$REDIS_PASSWORD --appendonly yes --appendfsync everysec
      else
        redis-server --appendonly yes --appendfsync everysec
      fi
      "
    volumes:
      - redis_data:/data
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "0.25"
          memory: 256M
        reservations:
          cpus: "0.1"
          memory: 128M
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep PONG"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - mail-alias-network
  # MailHog: SMTP server (for MPRS to send to and receive from) and Web UI
  # Optional: uncomment for local development and testing
  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "1025:1025" # Standard SMTP port for receiving email (MPRS sends here)
      - "8025:8025" # Web UI for viewing received emails (access via http://localhost:8025)
    networks:
      - mail-alias-network

networks:
  mail-alias-network:
    driver: bridge

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  redis_data:
    driver: local
