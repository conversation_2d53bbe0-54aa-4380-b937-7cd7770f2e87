package main

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/jackc/pgx/v5/tracelog"
	"github.com/rs/zerolog"
)

// Pool wraps pgxpool.Pool with additional functionality
type Pool struct {
	*pgxpool.Pool
	logger *zerolog.Logger
	config *Config
}

// Config holds database configuration
type Config struct {
	Host            string        `mapstructure:"host"`
	Port            int           `mapstructure:"port"`
	Database        string        `mapstructure:"database"`
	Username        string        `mapstructure:"username"`
	Password        string        `mapstructure:"password"`
	SSLMode         string        `mapstructure:"ssl_mode"`
	MaxConns        int32         `mapstructure:"max_conns"`
	MinConns        int32         `mapstructure:"min_conns"`
	MaxConnLifetime time.Duration `mapstructure:"max_conn_lifetime"`
	MaxConnIdleTime time.Duration `mapstructure:"max_conn_idle_time"`
	ConnectTimeout  time.Duration `mapstructure:"connect_timeout"`
}

// DefaultConfig returns a default database configuration
func DefaultConfig() *Config {
	return &Config{
		Host:            "localhost",
		Port:            5432,
		Database:        "mts_db",
		Username:        "postgres",
		Password:        "postgres",
		SSLMode:         "disable",
		MaxConns:        25,
		MinConns:        5,
		MaxConnLifetime: time.Hour,
		MaxConnIdleTime: time.Minute * 30,
		ConnectTimeout:  time.Second * 30,
	}
}

// DSN returns the database connection string
func (c *Config) DSN() string {
	return fmt.Sprintf(
		"host=%s port=%d dbname=%s user=%s password=%s sslmode=%s",
		c.Host, c.Port, c.Database, c.Username, c.Password, c.SSLMode,
	)
}

// NewPool creates a new database connection pool
func NewPool(ctx context.Context, config *Config, logger *zerolog.Logger) (*Pool, error) {
	// Configure pgxpool
	poolConfig, err := pgxpool.ParseConfig(config.DSN())
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// Set pool configuration
	poolConfig.MaxConns = config.MaxConns
	poolConfig.MinConns = config.MinConns
	poolConfig.MaxConnLifetime = config.MaxConnLifetime
	poolConfig.MaxConnIdleTime = config.MaxConnIdleTime

	// Set connection timeout
	poolConfig.ConnConfig.ConnectTimeout = config.ConnectTimeout

	// Configure logging for pgx using tracelog
	poolConfig.ConnConfig.Tracer = &tracelog.TraceLog{
		Logger:   &pgxLogger{logger: logger},
		LogLevel: tracelog.LogLevelWarn,
	}

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	dbPool := &Pool{
		Pool:   pool,
		logger: logger,
		config: config,
	}

	return dbPool, nil
}

// Connect establishes the database connection and performs initial ping
func (p *Pool) Connect(ctx context.Context) error {
	// Test the connection with ping
	if err := p.Ping(ctx); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	// Get pool stats to verify connection
	//stats := p.Pool.Stat()

	return nil
}

// Ping tests the database connection
func (p *Pool) Ping(ctx context.Context) error {
	// Create a context with timeout for ping
	pingCtx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()

	// Perform ping
	if err := p.Pool.Ping(pingCtx); err != nil {
		p.logger.Error().Err(err).Msg("Database ping failed")
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// HealthCheck performs a comprehensive health check
func (p *Pool) HealthCheck(ctx context.Context) error {
	// Check if pool is closed
	if p.Pool == nil {
		return fmt.Errorf("database pool is nil")
	}

	// Ping the database
	if err := p.Ping(ctx); err != nil {
		return err
	}

	// Check pool stats
	stats := p.Pool.Stat()
	if stats.TotalConns() == 0 {
		return fmt.Errorf("no database connections available")
	}

	// Test a simple query
	var result int
	err := p.Pool.QueryRow(ctx, "SELECT 1").Scan(&result)
	if err != nil {
		return fmt.Errorf("failed to execute test query: %w", err)
	}

	if result != 1 {
		return fmt.Errorf("unexpected result from test query: got %d, expected 1", result)
	}

	return nil
}

// Close closes the database connection pool
func (p *Pool) Close() {
	if p.Pool != nil {
		p.Pool.Close()
	}
}

// GetStats returns current pool statistics
func (p *Pool) GetStats() map[string]interface{} {
	if p.Pool == nil {
		return map[string]interface{}{
			"status": "disconnected",
		}
	}

	stats := p.Pool.Stat()
	return map[string]interface{}{
		"status":         "connected",
		"total_conns":    stats.TotalConns(),
		"idle_conns":     stats.IdleConns(),
		"acquired_conns": stats.AcquiredConns(),
		"max_conns":      p.config.MaxConns,
		"min_conns":      p.config.MinConns,
	}
}

// pgxLogger implements tracelog.Logger interface for zerolog integration
type pgxLogger struct {
	logger *zerolog.Logger
}

func (l *pgxLogger) Log(ctx context.Context, level tracelog.LogLevel, msg string, data map[string]interface{}) {
	var logEvent *zerolog.Event

	switch level {
	case tracelog.LogLevelTrace:
		logEvent = l.logger.Trace()
	case tracelog.LogLevelDebug:
		logEvent = l.logger.Debug()
	case tracelog.LogLevelInfo:
		logEvent = l.logger.Info()
	case tracelog.LogLevelWarn:
		logEvent = l.logger.Warn()
	case tracelog.LogLevelError:
		logEvent = l.logger.Error()
	default:
		logEvent = l.logger.Info()
	}

	if data != nil {
		logEvent = logEvent.Fields(data)
	}

	logEvent.Msg(msg)
}
