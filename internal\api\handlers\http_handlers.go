package handlers

import (
	"encoding/json"
	"errors"
	"net/http"
	"slices"
	"strconv"
	"strings"
	"time"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/api/middleware"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/commandhandlers"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/commands"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/monitoring"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/queries"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/queryhandlers"
	"github.com/go-chi/chi/v5"
	"github.com/rs/zerolog"
)

func CreateAliasHTTPHandler(handler *commandhandlers.CreateAliasCommandHandler, logger *zerolog.Logger, excludedDomains []string) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var cmd commands.CreateAliasCommand

		// Check if the original recipient email is in the excluded domains
		if slices.Contains(excludedDomains, cmd.OriginalRecipientEmail) {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Recipient email is in the excluded domains",
			}, logger)
			return
		}

		if err := json.NewDecoder(r.Body).Decode(&cmd); err != nil {
			if strings.Contains(err.Error(), "parsing time") {
				SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
					Code:    "VALIDATION_ERROR",
					Message: "ExpiresAt must be a valid time.",
				}, logger)
				return
			}

			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Error decoding request body",
			}, logger)
			monitoring.AliasCreationFailuresTotal.WithLabelValues("validation_error").Inc()
			return
		}

		if ok := ValidateAndRespond(w, middleware.GetValidator(r.Context()), cmd, logger); !ok {
			return
		}

		if cmd.ExpiresAt.Before(time.Now()) {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "ExpiresAt must be in the future.",
			}, logger)
			return
		}

		// Validate purpose value is in the list of valid purposes
		if !slices.Contains(core.AllPurposes, cmd.Purpose) {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid purpose value. Valid purposes are: " + core.AllPurposesString(),
			}, logger)
			return
		}

		if core.IsPurposeRequiresSenderEmail(cmd.Purpose) && cmd.OriginalSenderEmail == nil {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Original sender email is required for this purpose.",
			}, logger)
			return
		}

		monitoring.AliasCreationAttemptsTotal.Inc()
		result, err := handler.Handle(r.Context(), &cmd)

		if err != nil {
			if errors.Is(err, core.ErrAliasConflict) {
				SendApiResponse(w, http.StatusConflict, nil, false, &ErrorResponse{
					Code:    "ALIAS_CONFLICT",
					Message: "Alias already exists",
				}, logger)
				return
			}
			SendApiResponse(w, http.StatusInternalServerError, nil, false, &ErrorResponse{
				Code:    "INTERNAL_SERVER_ERROR",
				Message: "Internal server error: " + err.Error(),
			}, logger)
			monitoring.AliasCreationFailuresTotal.WithLabelValues("internal_error").Inc()
			return
		}

		SendApiResponse(w, http.StatusCreated, result, true, nil, logger)
	}
}

func UpdateAliasSenderEmailHTTPHandler(handler *commandhandlers.UpdateAliasSenderEmailCommandHandler, logger *zerolog.Logger) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var cmd commands.UpdateAliasSenderEmailCommand
		if err := json.NewDecoder(r.Body).Decode(&cmd); err != nil {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: err.Error(),
			}, logger)
			return
		}

		if ok := ValidateAndRespond(w, middleware.GetValidator(r.Context()), cmd, logger); !ok {
			return
		}

		monitoring.AliasUpdateAttemptsTotal.Inc()
		result, err := handler.Handle(r.Context(), &cmd)

		if err != nil {
			if errors.Is(err, core.ErrAliasNotFound) {
				SendApiResponse(w, http.StatusNotFound, nil, false, &ErrorResponse{
					Code:    "ALIAS_NOT_FOUND",
					Message: "Alias not found",
				}, logger)
				return
			}
			SendApiResponse(w, http.StatusInternalServerError, nil, false, &ErrorResponse{
				Code:    "INTERNAL_SERVER_ERROR",
				Message: "Internal server error: " + err.Error(),
			}, logger)
			monitoring.AliasUpdateFailuresTotal.WithLabelValues("internal_error").Inc()
			return
		}

		SendApiResponse(w, http.StatusOK, result, true, nil, logger)
	}
}

func GetAliasByIDHTTPHandler(handler *queryhandlers.GetAliasByIDQueryHandler, logger *zerolog.Logger) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		aliasID := chi.URLParam(r, "aliasID")

		monitoring.AliasLookupTotal.Inc()

		alias, err := handler.Handle(r.Context(), &queries.GetAliasByIDQuery{AliasID: aliasID})

		if err != nil {
			if errors.Is(err, core.ErrAliasNotFound) {
				SendApiResponse(w, http.StatusNotFound, nil, false, &ErrorResponse{
					Code:    "ALIAS_NOT_FOUND",
					Message: "Alias not found",
				}, logger)
				return
			}
			if errors.Is(err, core.ErrAliasExpired) {
				SendApiResponse(w, http.StatusNotFound, nil, false, &ErrorResponse{
					Code:    "ALIAS_EXPIRED",
					Message: "Alias has expired",
				}, logger)
				return
			}

			SendApiResponse(w, http.StatusInternalServerError, nil, false, &ErrorResponse{
				Code:    "INTERNAL_SERVER_ERROR",
				Message: "Internal server error: " + err.Error(),
			}, logger)
			monitoring.AliasLookupErrorsTotal.WithLabelValues("internal_error").Inc()
			return
		}
		SendApiResponse(w, http.StatusOK, alias, true, nil, logger)
	}
}

func GetAliasByContextFingerprintHTTPHandler(handler *queryhandlers.GetAliasByContextFingerprintQueryHandler, logger *zerolog.Logger) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {

		contextFingerprint := chi.URLParam(r, "contextFingerprint")

		monitoring.AliasLookupTotal.Inc()

		alias, err := handler.Handle(r.Context(), &queries.GetAliasByContextFingerprintQuery{ContextFingerprint: contextFingerprint})

		if err != nil {
			if errors.Is(err, core.ErrAliasNotFound) {
				SendApiResponse(w, http.StatusNotFound, nil, false, &ErrorResponse{
					Code:    "ALIAS_NOT_FOUND",
					Message: "Alias not found",
				}, logger)
				return
			}
			SendApiResponse(w, http.StatusInternalServerError, nil, false, &ErrorResponse{
				Code:    "INTERNAL_SERVER_ERROR",
				Message: "Internal server error: " + err.Error(),
			}, logger)
			monitoring.AliasLookupErrorsTotal.WithLabelValues("internal_error").Inc()
			return
		}
		SendApiResponse(w, http.StatusOK, alias, true, nil, logger)
	}
}

func GetAliasesByLegacyFingerprintHTTPHandler(handler *queryhandlers.GetAliasesByLegacyFingerprintQueryHandler, logger *zerolog.Logger) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {

		fingerprint := r.URL.Query().Get("fingerprint")
		limit := r.URL.Query().Get("limit")
		offset := r.URL.Query().Get("offset")

		if fingerprint == "" {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Fingerprint is required.",
			}, logger)
			return
		}

		limitInt, offsetInt, err := handleLimitAndOffset(limit, offset)
		if err != nil {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid limit or offset value.",
			}, logger)
			return
		}

		monitoring.AliasLookupTotal.Inc()

		aliases, err := handler.Handle(r.Context(), &queries.GetAliasesByLegacyFingerprintQuery{Fingerprint: fingerprint, Limit: limitInt, Offset: offsetInt})

		if err != nil {
			if errors.Is(err, core.ErrAliasNotFound) {
				SendApiResponse(w, http.StatusNotFound, nil, false, &ErrorResponse{
					Code:    "ALIAS_NOT_FOUND",
					Message: "Alias not found",
				}, logger)
				return
			}
			SendApiResponse(w, http.StatusInternalServerError, nil, false, &ErrorResponse{
				Code:    "INTERNAL_SERVER_ERROR",
				Message: "Internal server error: " + err.Error(),
			}, logger)
			monitoring.AliasLookupErrorsTotal.WithLabelValues("internal_error").Inc()
			return
		}
		SendApiResponse(w, http.StatusOK, aliases, true, nil, logger)
	}
}

func GetAliasByLocatorAndPurposeHTTPHandler(handler *queryhandlers.GetAliasByLocatorAndPurposeQueryHandler, logger *zerolog.Logger) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		locator := chi.URLParam(r, "locator")
		purpose := r.URL.Query().Get("purpose")

		// Validate purpose value is in the list of valid purposes
		if !slices.Contains(core.AllPurposes, purpose) {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid purpose value. Valid purposes are: " + core.AllPurposesString(),
			}, logger)
			return
		}

		monitoring.AliasLookupTotal.Inc()
		alias, err := handler.Handle(r.Context(), &queries.GetAliasByLocatorAndPurposeQuery{Locator: locator, Purpose: purpose})

		if err != nil {
			if errors.Is(err, core.ErrAliasNotFound) {
				SendApiResponse(w, http.StatusNotFound, nil, false, &ErrorResponse{
					Code:    "ALIAS_NOT_FOUND",
					Message: "Alias not found",
				}, logger)
				return
			}
			SendApiResponse(w, http.StatusInternalServerError, nil, false, &ErrorResponse{
				Code:    "INTERNAL_SERVER_ERROR",
				Message: "Internal server error: " + err.Error(),
			}, logger)
			monitoring.AliasLookupErrorsTotal.WithLabelValues("internal_error").Inc()
			return
		}

		SendApiResponse(w, http.StatusOK, alias, true, nil, logger)
	}
}

func GetAliasesByOriginalRecipientEmailHTTPHandler(handler *queryhandlers.GetAliasesByOriginalRecipientEmailQueryHandler, logger *zerolog.Logger) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		originalRecipientEmail := r.URL.Query().Get("original_recipient_email")
		purpose := r.URL.Query().Get("purpose")
		limit := r.URL.Query().Get("limit")
		offset := r.URL.Query().Get("offset")

		if originalRecipientEmail == "" {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Original recipient email is required.",
			}, logger)
			return
		}

		limitInt, offsetInt, err := handleLimitAndOffset(limit, offset)
		if err != nil {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid limit or offset value.",
			}, logger)
			return
		}

		monitoring.AliasLookupTotal.Inc()

		aliases, err := handler.Handle(r.Context(), &queries.GetAliasesByOriginalRecipientEmailQuery{OriginalRecipientEmail: originalRecipientEmail, Purpose: purpose, Limit: limitInt, Offset: offsetInt})

		if err != nil {
			if errors.Is(err, core.ErrAliasNotFound) {
				SendApiResponse(w, http.StatusNotFound, nil, false, &ErrorResponse{
					Code:    "ALIAS_NOT_FOUND",
					Message: "Alias not found",
				}, logger)
				return
			}
			SendApiResponse(w, http.StatusInternalServerError, nil, false, &ErrorResponse{
				Code:    "INTERNAL_SERVER_ERROR",
				Message: "Internal server error: " + err.Error(),
			}, logger)
			monitoring.AliasLookupErrorsTotal.WithLabelValues("internal_error").Inc()
			return
		}

		SendApiResponse(w, http.StatusOK, aliases, true, nil, logger)
	}
}

func GetAliasesByHostUserIDHTTPHandler(handler *queryhandlers.GetAliasesByHostUserIDQueryHandler, logger *zerolog.Logger) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		hostUserID := chi.URLParam(r, "hostUserID")
		limit := r.URL.Query().Get("limit")
		offset := r.URL.Query().Get("offset")

		limitInt, offsetInt, err := handleLimitAndOffset(limit, offset)
		if err != nil {
			SendApiResponse(w, http.StatusBadRequest, nil, false, &ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid limit or offset value.",
			}, logger)
			return
		}

		monitoring.AliasLookupTotal.Inc()

		aliases, err := handler.Handle(r.Context(), &queries.GetAliasesByHostUserIDQuery{HostUserID: hostUserID, Limit: limitInt, Offset: offsetInt})
		if err != nil {
			if errors.Is(err, core.ErrAliasNotFound) {
				SendApiResponse(w, http.StatusNotFound, nil, false, &ErrorResponse{
					Code:    "ALIAS_NOT_FOUND",
					Message: "Alias not found",
				}, logger)
				return
			}
			SendApiResponse(w, http.StatusInternalServerError, nil, false, &ErrorResponse{
				Code:    "INTERNAL_SERVER_ERROR",
				Message: "Internal server error: " + err.Error(),
			}, logger)
			monitoring.AliasLookupErrorsTotal.WithLabelValues("internal_error").Inc()
			return
		}

		SendApiResponse(w, http.StatusOK, aliases, true, nil, logger)
	}
}

func handleLimitAndOffset(limit, offset string) (int, int, error) {
	limitInt := 10
	offsetInt := 0
	var err error

	if limit != "" {
		limitInt, err = strconv.Atoi(limit)
		if err != nil {
			return 0, 0, err
		}
	}

	if offset != "" {
		offsetInt, err = strconv.Atoi(offset)
		if err != nil {
			return 0, 0, err
		}
	}

	return limitInt, offsetInt, nil
}
