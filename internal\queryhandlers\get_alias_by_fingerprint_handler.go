package queryhandlers

import (
	"context"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/queries"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
)

type GetAliasByContextFingerprintQueryHandler struct {
	aliasRepo repository.AliasRepository
}

func NewGetAliasByContextFingerprintQueryHandler(aliasRepo repository.AliasRepository) *GetAliasByContextFingerprintQueryHandler {
	return &GetAliasByContextFingerprintQueryHandler{aliasRepo: aliasRepo}
}

func (h *GetAliasByContextFingerprintQueryHandler) Handle(ctx context.Context, query *queries.GetAliasByContextFingerprintQuery) (*core.Alias, error) {

	alias, err := h.aliasRepo.GetAliasByContextFingerprint(ctx, query.ContextFingerprint)
	if err != nil {
		return nil, err
	}

	aliases := core.FilterActiveAndNotExpiredAliases([]*core.Alias{alias})

	if len(aliases) == 0 {
		return nil, core.ErrAliasNotFound
	}

	return aliases[0], nil
}
