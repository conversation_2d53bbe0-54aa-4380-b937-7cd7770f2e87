{"info": {"_postman_id": "53e1133d-cf4e-4291-b34b-5996e76292e5", "name": "Mail Alias Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "18343496"}, "item": [{"name": "Health & Status", "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["// Basic tests for health check API\r", "\r", "// Test for a successful response status code\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Test for response time\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "// Test for content-type header\r", "pm.test(\"Content-Type is JSON\", function () {\r", "    pm.response.to.have.header(\"Content-Type\", \"application/json\");\r", "});\r", "\r", "// Test for response body to be a valid JSON\r", "pm.test(\"Response body is a valid JSON\", function () {\r", "    pm.expect(pm.response.json()).to.be.an(\"object\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "type": "text", "disabled": true}], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check the health status of the service"}, "response": []}, {"name": "Heartbeat", "event": [{"listen": "test", "script": {"exec": ["// Check if the response status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Check if the response time is less than 200ms\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "// Check if the Content-Type header is present and is of type application/json\r", "pm.test(\"Content-Type is application/json\", function () {\r", "    pm.response.to.have.header(\"Content-Type\", \"text/plain\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/heartbeat", "host": ["{{base_url}}"], "path": ["heartbeat"]}, "description": "Simple heartbeat endpoint to check if service is alive"}, "response": []}]}, {"name": "Bidirectional Communication Aliases", "item": [{"name": "Create Guest-Host <PERSON><PERSON> (Hotel)", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test for status code\r", "pm.test('Status code is 201', () => {\r", "    pm.expect(pm.response.code).to.equal(201);\r", "});\r", "\r", "// Test for response time\r", "pm.test('Response time is less than 300ms', () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(300);\r", "});\r", "\r", "// Test for content type\r", "pm.test('Content-Type is application/json', () => {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.equal('application/json');\r", "});\r", "\r", "// Test for success field\r", "pm.test('Success field is true', () => {\r", "    pm.expect(response.success).to.be.true;\r", "});\r", "\r", "// Test for data structure\r", "pm.test('Data structure contains required fields', () => {\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'masked_recipient_email',\r", "        'locator',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'product_type'\r", "    ];\r", "    \r", "    requiredFields.forEach(field => {\r", "        pm.expect(response.data).to.have.property(field);\r", "    });\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"original_recipient_email\": \"<EMAIL>\",\n  \"locator\": \"helderl123\",\n  \"purpose\": \"guest_host_chat_hotel\",\n  \"product_type\": \"hotel\",\n  \"channel\": 1,\n  \"communication_direction\": \"bidirectional\",\n  \"expires_at\": \"2026-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}, "description": "Create an alias for bidirectional communication between guest and hotel staff"}, "response": []}, {"name": "Create Guest-Host <PERSON><PERSON> (Vacation Home)", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test for status code\r", "pm.test('Status code is 201', () => {\r", "    pm.expect(pm.response.code).to.equal(201);\r", "});\r", "\r", "// Test for response time\r", "pm.test('Response time is less than 300ms', () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(300);\r", "});\r", "\r", "// Test for content type\r", "pm.test('Content-Type is application/json', () => {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.equal('application/json');\r", "});\r", "\r", "// Test for success field\r", "pm.test('Success field is true', () => {\r", "    pm.expect(response.success).to.be.true;\r", "});\r", "\r", "// Test for data structure\r", "pm.test('Data structure contains required fields', () => {\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'masked_recipient_email',\r", "        'locator',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'product_type'\r", "    ];\r", "    \r", "    requiredFields.forEach(field => {\r", "        pm.expect(response.data).to.have.property(field);\r", "    });\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"original_recipient_email\": \"<EMAIL>\",\n    \"original_sender_email\": \"<EMAIL>\",\n    \"locator\": \"VH789012\",\n    \"guest_user_id\": \"550e8400-e29b-41d4-a716-446655440003\",\n    \"host_user_id\": \"550e8400-e29b-41d4-a716-446655440004\",\n    \"channel\": 1,\n    \"purpose\": \"guest_host_chat_vacation_home\",\n    \"communication_direction\": \"bidirectional\",\n    \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}, "description": "Create an alias for bidirectional communication between guest and vacation home owner"}, "response": []}, {"name": "Create Customer Support Thread", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test for status code\r", "pm.test('Status code is 201', () => {\r", "    pm.expect(pm.response.code).to.equal(201);\r", "});\r", "\r", "// Test for response time\r", "pm.test('Response time is less than 300ms', () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(300);\r", "});\r", "\r", "// Test for content type\r", "pm.test('Content-Type is application/json', () => {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.equal('application/json');\r", "});\r", "\r", "// Test for success field\r", "pm.test('Success field is true', () => {\r", "    pm.expect(response.success).to.be.true;\r", "});\r", "\r", "// Test for data structure\r", "pm.test('Data structure contains required fields', () => {\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'masked_recipient_email',\r", "        'locator',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'product_type'\r", "    ];\r", "    \r", "    requiredFields.forEach(field => {\r", "        pm.expect(response.data).to.have.property(field);\r", "    });\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"original_recipient_email\": \"<EMAIL>\",\n    \"locator\": \"SUP901234\",\n    \"purpose\": \"customer_support_thread\",\n    \"channel\": 4,\n    \"communication_direction\": \"bidirectional\",\n    \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}, "description": "Create an alias for bidirectional communication with customer support"}, "response": []}], "description": "Aliases for bidirectional communication between parties"}, {"name": "Outbound Communication Aliases", "item": [{"name": "Create Booking Confirmation Alias", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test for status code\r", "pm.test('Status code is 201', () => {\r", "    pm.expect(pm.response.code).to.equal(201);\r", "});\r", "\r", "// Test for response time\r", "pm.test('Response time is less than 300ms', () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(300);\r", "});\r", "\r", "// Test for content type\r", "pm.test('Content-Type is application/json', () => {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.equal('application/json');\r", "});\r", "\r", "// Test for success field\r", "pm.test('Success field is true', () => {\r", "    pm.expect(response.success).to.be.true;\r", "});\r", "\r", "// Test for data structure\r", "pm.test('Data structure contains required fields', () => {\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'masked_recipient_email',\r", "        'locator',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'product_type'\r", "    ];\r", "    \r", "    requiredFields.forEach(field => {\r", "        pm.expect(response.data).to.have.property(field);\r", "    });\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"original_recipient_email\": \"<EMAIL>\",\n  \"locator\": \"BK567890\",\n  \"guest_user_id\": \"550e8400-e29b-41d4-a716-446655440007\",\n  \"purpose\": \"system_booking_confirmation\",\n  \"channel\": 1,\n  \"communication_direction\": \"outbound\",\n  \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}, "description": "Create an alias for sending booking confirmation emails"}, "response": []}, {"name": "Create Flight Confirmation Alias", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test for status code\r", "pm.test('Status code is 201', () => {\r", "    pm.expect(pm.response.code).to.equal(201);\r", "});\r", "\r", "// Test for response time\r", "pm.test('Response time is less than 300ms', () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(300);\r", "});\r", "\r", "// Test for content type\r", "pm.test('Content-Type is application/json', () => {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.equal('application/json');\r", "});\r", "\r", "// Test for success field\r", "pm.test('Success field is true', () => {\r", "    pm.expect(response.success).to.be.true;\r", "});\r", "\r", "// Test for data structure\r", "pm.test('Data structure contains required fields', () => {\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'masked_recipient_email',\r", "        'locator',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'product_type'\r", "    ];\r", "    \r", "    requiredFields.forEach(field => {\r", "        pm.expect(response.data).to.have.property(field);\r", "    });\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"original_recipient_email\": \"<EMAIL>\",\n  \"locator\": \"FL123789\",\n  \"guest_user_id\": \"550e8400-e29b-41d4-a716-446655440008\",\n  \"purpose\": \"system_flight_confirmation\",\n  \"channel\": 2,\n  \"communication_direction\": \"outbound\",\n  \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}, "description": "Create an alias for sending flight-specific booking confirmations"}, "response": []}, {"name": "Create Payment Notification Alias", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test for status code\r", "pm.test('Status code is 201', () => {\r", "    pm.expect(pm.response.code).to.equal(201);\r", "});\r", "\r", "// Test for response time\r", "pm.test('Response time is less than 300ms', () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(300);\r", "});\r", "\r", "// Test for content type\r", "pm.test('Content-Type is application/json', () => {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.equal('application/json');\r", "});\r", "\r", "// Test for success field\r", "pm.test('Success field is true', () => {\r", "    pm.expect(response.success).to.be.true;\r", "});\r", "\r", "// Test for data structure\r", "pm.test('Data structure contains required fields', () => {\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'masked_recipient_email',\r", "        'locator',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'product_type'\r", "    ];\r", "    \r", "    requiredFields.forEach(field => {\r", "        pm.expect(response.data).to.have.property(field);\r", "    });\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"original_recipient_email\": \"<EMAIL>\",\n  \"locator\": \"PAY789456\",\n  \"guest_user_id\": \"550e8400-e29b-41d4-a716-446655440010\",\n  \"purpose\": \"system_payment_notification\",\n  \"channel\": 4,\n  \"communication_direction\": \"outbound\",\n  \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}, "description": "Create an alias for sending payment success/failure/refund notifications"}, "response": []}, {"name": "Create Marketing Campaign Alias", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test for status code\r", "pm.test('Status code is 201', () => {\r", "    pm.expect(pm.response.code).to.equal(201);\r", "});\r", "\r", "// Test for response time\r", "pm.test('Response time is less than 300ms', () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(300);\r", "});\r", "\r", "// Test for content type\r", "pm.test('Content-Type is application/json', () => {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.equal('application/json');\r", "});\r", "\r", "// Test for success field\r", "pm.test('Success field is true', () => {\r", "    pm.expect(response.success).to.be.true;\r", "});\r", "\r", "// Test for data structure\r", "pm.test('Data structure contains required fields', () => {\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'masked_recipient_email',\r", "        'locator',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'product_type'\r", "    ];\r", "    \r", "    requiredFields.forEach(field => {\r", "        pm.expect(response.data).to.have.property(field);\r", "    });\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"original_recipient_email\": \"<EMAIL>\",\n  \"guest_user_id\": \"550e8400-e29b-41d4-a716-446655440013\",\n  \"locator\": \"HOTSALE\",\n  \"purpose\": \"marketing_campaign\",\n  \"channel\": 7,\n  \"communication_direction\": \"outbound\",\n  \"expires_at\": \"2025-09-30T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}, "description": "Create an alias for marketing campaign emails (locator optional)"}, "response": []}], "description": "Aliases for one-way outbound communication from the system"}, {"name": "Retrieve Aliases", "item": [{"name": "Get Alias by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.expect(pm.response.status).to.eql(\"OK\");\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "\r", "pm.test(\"Response has the required fields\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.data).to.be.an('object');\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'original_sender_email',\r", "        'masked_recipient_email',\r", "        'masked_sender_email',\r", "        'locator',\r", "        'guest_user_id',\r", "        'host_user_id',\r", "        'fingerprint',\r", "        'context_fingerprint',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'last_used_at',\r", "        'product_type'\r", "    ];\r", "\r", "    requiredFields.forEach(function (field) {\r", "        pm.expect(responseData.data).to.have.property(field);\r", "    });\r", "});\r", "\r", "\r", "pm.test(\"Email fields are in a valid format\", function () {\r", "  const responseData = pm.response.json();\r", "\r", "  const emailFields = [\r", "    responseData.data.original_recipient_email,\r", "    responseData.data.original_sender_email,\r", "    responseData.data.masked_recipient_email,\r", "    responseData.data.masked_sender_email,\r", "  ];\r", "\r", "  // This is the corrected, more flexible regex\r", "  const emailRegex = /^[^\\s@]+@([^\\s@.]+\\.)+[^\\s@.]+$/;\r", "\r", "  emailFields.forEach(function (email) {\r", "    // Ensure the email is not null or undefined before testing\r", "    if (email) {\r", "      pm.expect(email).to.match(\r", "        emailRegex,\r", "        `Email format is invalid for: ${email}`\r", "      );\r", "    }\r", "  });\r", "});\r", "\r", "\r", "pm.test(\"Channel is a non-negative integer\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.data).to.exist;\r", "    pm.expect(responseData.data.channel).to.be.a('number').and.to.be.at.least(0);\r", "});\r", "\r", "pm.test(\"Success flag is true\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.have.property('success');\r", "    pm.expect(responseData.success).to.be.true\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/alias/{{lookup_alias_id}}", "host": ["{{base_url}}"], "path": ["api", "alias", "{{lookup_alias_id}}"]}, "description": "Retrieve a specific alias by its ID"}, "response": []}, {"name": "Get Alias by Context", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.expect(pm.response.status).to.eql(\"OK\");\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "\r", "pm.test(\"Response has the required fields\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.data).to.be.an('object');\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'original_sender_email',\r", "        'masked_recipient_email',\r", "        'masked_sender_email',\r", "        'locator',\r", "        'guest_user_id',\r", "        'host_user_id',\r", "        'fingerprint',\r", "        'context_fingerprint',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'last_used_at',\r", "        'product_type'\r", "    ];\r", "\r", "    requiredFields.forEach(function (field) {\r", "        pm.expect(responseData.data).to.have.property(field);\r", "    });\r", "});\r", "\r", "\r", "pm.test(\"Email fields are in a valid format\", function () {\r", "  const responseData = pm.response.json();\r", "\r", "  const emailFields = [\r", "    responseData.data.original_recipient_email,\r", "    responseData.data.original_sender_email,\r", "    responseData.data.masked_recipient_email,\r", "    responseData.data.masked_sender_email,\r", "  ];\r", "\r", "  // This is the corrected, more flexible regex\r", "  const emailRegex = /^[^\\s@]+@([^\\s@.]+\\.)+[^\\s@.]+$/;\r", "\r", "  emailFields.forEach(function (email) {\r", "    // Ensure the email is not null or undefined before testing\r", "    if (email) {\r", "      pm.expect(email).to.match(\r", "        emailRegex,\r", "        `Email format is invalid for: ${email}`\r", "      );\r", "    }\r", "  });\r", "});\r", "\r", "\r", "pm.test(\"Channel is a non-negative integer\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.data).to.exist;\r", "    pm.expect(responseData.data.channel).to.be.a('number').and.to.be.at.least(0);\r", "});\r", "\r", "pm.test(\"Success flag is true\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.have.property('success');\r", "    pm.expect(responseData.success).to.be.true\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/alias/context/{{lookup_contextfingerprint}}", "host": ["{{base_url}}"], "path": ["api", "alias", "context", "{{lookup_contextfingerprint}}"], "query": [{"key": "", "value": null, "disabled": true}]}}, "response": []}, {"name": "Get Aliases by Fingerprint", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.expect(pm.response.status).to.eql(\"OK\");\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "pm.test(\"Success flag is true\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.have.property('success');\r", "    pm.expect(responseData.success).to.be.true\r", "});\r", "\r", "// Helper function to safely normalize the response data into an array.\r", "// Handles cases where data is an object, an array, or null/undefined.\r", "function getAliasesAsArray(response) {\r", "  const responseData = response.json();\r", "  if (!responseData || !responseData.data) {\r", "    return []; // Return empty array if data property is missing or null\r", "  }\r", "  return Array.isArray(responseData.data)\r", "    ? responseData.data\r", "    : [responseData.data];\r", "}\r", "\r", "// This test runs on every response to ensure basic success criteria are met.\r", "pm.test(\"Response is successful (Status 200 OK and success:true)\", function () {\r", "  pm.response.to.have.status(200);\r", "  const responseData = pm.response.json();\r", "  pm.expect(responseData.success, \"The 'success' field should be true\").to.be\r", "    .true;\r", "  pm.expect(responseData, \"Response should have a 'data' property\").to.have.property(\r", "    \"data\"\r", "  );\r", "});\r", "\r", "// Normalize the data into an array for consistent processing.\r", "const aliases = getAliasesAsArray(pm.response);\r", "\r", "// --- Conditional Tests ---\r", "// These tests will only run if the 'data' array is not empty.\r", "if (aliases.length > 0) {\r", "  pm.test(\"All aliases have the required fields\", function () {\r", "    const requiredFields = [\r", "      \"alias_id\",\r", "      \"original_recipient_email\",\r", "      \"original_sender_email\",\r", "      \"masked_recipient_email\",\r", "      \"masked_sender_email\",\r", "      \"locator\",\r", "      \"guest_user_id\",\r", "      \"host_user_id\",\r", "      \"fingerprint\",\r", "      \"context_fingerprint\",\r", "      \"purpose\",\r", "      \"channel\",\r", "      \"communication_direction\",\r", "      \"creator_service\",\r", "      \"created_at\",\r", "      \"expires_at\",\r", "      \"status\",\r", "      \"last_used_at\",\r", "      \"product_type\",\r", "    ];\r", "\r", "    aliases.forEach(function (alias, index) {\r", "      pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.be.an(\"object\");\r", "      requiredFields.forEach(function (field) {\r", "        pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.have.property(field);\r", "      });\r", "    });\r", "  });\r", "\r", "  pm.test(\"Email fields are in a valid format for all aliases\", function () {\r", "    const emailRegex = /^[^\\s@]+@([^\\s@.]+\\.)+[^\\s@.]+$/;\r", "\r", "    aliases.forEach(function (alias, index) {\r", "      const emailFields = [\r", "        alias.original_recipient_email,\r", "        alias.original_sender_email,\r", "        alias.masked_recipient_email,\r", "        alias.masked_sender_email,\r", "      ];\r", "\r", "      emailFields.forEach(function (email) {\r", "        if (email) {\r", "          pm.expect(email).to.match(\r", "            emailRegex,\r", "            `Email format is invalid for alias at index ${index}: ${email}`\r", "          );\r", "        }\r", "      });\r", "    });\r", "  });\r", "\r", "  pm.test(\"Channel is a non-negative integer for all aliases\", function () {\r", "    aliases.forEach(function (alias, index) {\r", "      pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.have.property(\"channel\");\r", "      pm.expect(alias.channel, `Channel for alias at index ${index}`)\r", "        .to.be.a(\"number\")\r", "        .and.to.be.at.least(0);\r", "    });\r", "  });\r", "} else {\r", "  // This test runs only if the 'data' array is empty, confirming it's a valid state.\r", "  pm.test(\"Data array is empty, skipping alias-specific tests\", function () {\r", "    pm.expect(aliases).to.be.an(\"array\").with.lengthOf(0);\r", "  });\r", "}\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/aliases/fingerprint?fingerprint={{lookup_fingerprint}}", "host": ["{{base_url}}"], "path": ["api", "aliases", "fingerprint"], "query": [{"key": "limit", "value": "100", "disabled": true}, {"key": "offset", "value": "0", "disabled": true}, {"key": "fingerprint", "value": "{{lookup_fingerprint}}"}]}}, "response": []}, {"name": "Get Alias by <PERSON><PERSON>or", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.expect(pm.response.status).to.eql(\"OK\");\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "\r", "pm.test(\"Response has the required fields\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.data).to.be.an('object');\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'original_sender_email',\r", "        'masked_recipient_email',\r", "        'masked_sender_email',\r", "        'locator',\r", "        'guest_user_id',\r", "        'host_user_id',\r", "        'fingerprint',\r", "        'context_fingerprint',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'last_used_at',\r", "        'product_type'\r", "    ];\r", "\r", "    requiredFields.forEach(function (field) {\r", "        pm.expect(responseData.data).to.have.property(field);\r", "    });\r", "});\r", "\r", "\r", "pm.test(\"Email fields are in a valid format\", function () {\r", "  const responseData = pm.response.json();\r", "\r", "  const emailFields = [\r", "    responseData.data.original_recipient_email,\r", "    responseData.data.original_sender_email,\r", "    responseData.data.masked_recipient_email,\r", "    responseData.data.masked_sender_email,\r", "  ];\r", "\r", "  // This is the corrected, more flexible regex\r", "  const emailRegex = /^[^\\s@]+@([^\\s@.]+\\.)+[^\\s@.]+$/;\r", "\r", "  emailFields.forEach(function (email) {\r", "    // Ensure the email is not null or undefined before testing\r", "    if (email) {\r", "      pm.expect(email).to.match(\r", "        emailRegex,\r", "        `Email format is invalid for: ${email}`\r", "      );\r", "    }\r", "  });\r", "});\r", "\r", "\r", "pm.test(\"Channel is a non-negative integer\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.data).to.exist;\r", "    pm.expect(responseData.data.channel).to.be.a('number').and.to.be.at.least(0);\r", "});\r", "\r", "pm.test(\"Success flag is true\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.have.property('success');\r", "    pm.expect(responseData.success).to.be.true\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/alias/locator/{{lookup_locator}}?purpose=guest_host_chat_hotel", "host": ["{{base_url}}"], "path": ["api", "alias", "locator", "{{lookup_locator}}"], "query": [{"key": "purpose", "value": "guest_host_chat_hotel"}]}}, "response": []}, {"name": "Get Aliases by Original Recipient", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.expect(pm.response.status).to.eql(\"OK\");\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "pm.test(\"Success flag is true\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.have.property('success');\r", "    pm.expect(responseData.success).to.be.true\r", "});\r", "\r", "// Helper function to safely normalize the response data into an array.\r", "// Handles cases where data is an object, an array, or null/undefined.\r", "function getAliasesAsArray(response) {\r", "  const responseData = response.json();\r", "  if (!responseData || !responseData.data) {\r", "    return []; // Return empty array if data property is missing or null\r", "  }\r", "  return Array.isArray(responseData.data)\r", "    ? responseData.data\r", "    : [responseData.data];\r", "}\r", "\r", "// This test runs on every response to ensure basic success criteria are met.\r", "pm.test(\"Response is successful (Status 200 OK and success:true)\", function () {\r", "  pm.response.to.have.status(200);\r", "  const responseData = pm.response.json();\r", "  pm.expect(responseData.success, \"The 'success' field should be true\").to.be\r", "    .true;\r", "  pm.expect(responseData, \"Response should have a 'data' property\").to.have.property(\r", "    \"data\"\r", "  );\r", "});\r", "\r", "// Normalize the data into an array for consistent processing.\r", "const aliases = getAliasesAsArray(pm.response);\r", "\r", "// --- Conditional Tests ---\r", "// These tests will only run if the 'data' array is not empty.\r", "if (aliases.length > 0) {\r", "  pm.test(\"All aliases have the required fields\", function () {\r", "    const requiredFields = [\r", "      \"alias_id\",\r", "      \"original_recipient_email\",\r", "      \"original_sender_email\",\r", "      \"masked_recipient_email\",\r", "      \"masked_sender_email\",\r", "      \"locator\",\r", "      \"guest_user_id\",\r", "      \"host_user_id\",\r", "      \"fingerprint\",\r", "      \"context_fingerprint\",\r", "      \"purpose\",\r", "      \"channel\",\r", "      \"communication_direction\",\r", "      \"creator_service\",\r", "      \"created_at\",\r", "      \"expires_at\",\r", "      \"status\",\r", "      \"last_used_at\",\r", "      \"product_type\",\r", "    ];\r", "\r", "    aliases.forEach(function (alias, index) {\r", "      pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.be.an(\"object\");\r", "      requiredFields.forEach(function (field) {\r", "        pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.have.property(field);\r", "      });\r", "    });\r", "  });\r", "\r", "  pm.test(\"Email fields are in a valid format for all aliases\", function () {\r", "    const emailRegex = /^[^\\s@]+@([^\\s@.]+\\.)+[^\\s@.]+$/;\r", "\r", "    aliases.forEach(function (alias, index) {\r", "      const emailFields = [\r", "        alias.original_recipient_email,\r", "        alias.original_sender_email,\r", "        alias.masked_recipient_email,\r", "        alias.masked_sender_email,\r", "      ];\r", "\r", "      emailFields.forEach(function (email) {\r", "        if (email) {\r", "          pm.expect(email).to.match(\r", "            emailRegex,\r", "            `Email format is invalid for alias at index ${index}: ${email}`\r", "          );\r", "        }\r", "      });\r", "    });\r", "  });\r", "\r", "  pm.test(\"Channel is a non-negative integer for all aliases\", function () {\r", "    aliases.forEach(function (alias, index) {\r", "      pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.have.property(\"channel\");\r", "      pm.expect(alias.channel, `Channel for alias at index ${index}`)\r", "        .to.be.a(\"number\")\r", "        .and.to.be.at.least(0);\r", "    });\r", "  });\r", "} else {\r", "  // This test runs only if the 'data' array is empty, confirming it's a valid state.\r", "  pm.test(\"Data array is empty, skipping alias-specific tests\", function () {\r", "    pm.expect(aliases).to.be.an(\"array\").with.lengthOf(0);\r", "  });\r", "}\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/aliases/original_recipient?original_recipient_email={{lookup_original_recipient}}", "host": ["{{base_url}}"], "path": ["api", "aliases", "original_recipient"], "query": [{"key": "purpose", "value": "guest_host_chat_hotel", "disabled": true}, {"key": "original_recipient_email", "value": "{{lookup_original_recipient}}"}]}}, "response": []}, {"name": "Get Aliases by Host User ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.expect(pm.response.status).to.eql(\"OK\");\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "pm.test(\"Success flag is true\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.have.property('success');\r", "    pm.expect(responseData.success).to.be.true\r", "});\r", "\r", "// Helper function to safely normalize the response data into an array.\r", "// Handles cases where data is an object, an array, or null/undefined.\r", "function getAliasesAsArray(response) {\r", "  const responseData = response.json();\r", "  if (!responseData || !responseData.data) {\r", "    return []; // Return empty array if data property is missing or null\r", "  }\r", "  return Array.isArray(responseData.data)\r", "    ? responseData.data\r", "    : [responseData.data];\r", "}\r", "\r", "// This test runs on every response to ensure basic success criteria are met.\r", "pm.test(\"Response is successful (Status 200 OK and success:true)\", function () {\r", "  pm.response.to.have.status(200);\r", "  const responseData = pm.response.json();\r", "  pm.expect(responseData.success, \"The 'success' field should be true\").to.be\r", "    .true;\r", "  pm.expect(responseData, \"Response should have a 'data' property\").to.have.property(\r", "    \"data\"\r", "  );\r", "});\r", "\r", "// Normalize the data into an array for consistent processing.\r", "const aliases = getAliasesAsArray(pm.response);\r", "\r", "// --- Conditional Tests ---\r", "// These tests will only run if the 'data' array is not empty.\r", "if (aliases.length > 0) {\r", "  pm.test(\"All aliases have the required fields\", function () {\r", "    const requiredFields = [\r", "      \"alias_id\",\r", "      \"original_recipient_email\",\r", "      \"original_sender_email\",\r", "      \"masked_recipient_email\",\r", "      \"masked_sender_email\",\r", "      \"locator\",\r", "      \"guest_user_id\",\r", "      \"host_user_id\",\r", "      \"fingerprint\",\r", "      \"context_fingerprint\",\r", "      \"purpose\",\r", "      \"channel\",\r", "      \"communication_direction\",\r", "      \"creator_service\",\r", "      \"created_at\",\r", "      \"expires_at\",\r", "      \"status\",\r", "      \"last_used_at\",\r", "      \"product_type\",\r", "    ];\r", "\r", "    aliases.forEach(function (alias, index) {\r", "      pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.be.an(\"object\");\r", "      requiredFields.forEach(function (field) {\r", "        pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.have.property(field);\r", "      });\r", "    });\r", "  });\r", "\r", "  pm.test(\"Email fields are in a valid format for all aliases\", function () {\r", "    const emailRegex = /^[^\\s@]+@([^\\s@.]+\\.)+[^\\s@.]+$/;\r", "\r", "    aliases.forEach(function (alias, index) {\r", "      const emailFields = [\r", "        alias.original_recipient_email,\r", "        alias.original_sender_email,\r", "        alias.masked_recipient_email,\r", "        alias.masked_sender_email,\r", "      ];\r", "\r", "      emailFields.forEach(function (email) {\r", "        if (email) {\r", "          pm.expect(email).to.match(\r", "            emailRegex,\r", "            `Email format is invalid for alias at index ${index}: ${email}`\r", "          );\r", "        }\r", "      });\r", "    });\r", "  });\r", "\r", "  pm.test(\"Channel is a non-negative integer for all aliases\", function () {\r", "    aliases.forEach(function (alias, index) {\r", "      pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.have.property(\"channel\");\r", "      pm.expect(alias.channel, `Channel for alias at index ${index}`)\r", "        .to.be.a(\"number\")\r", "        .and.to.be.at.least(0);\r", "    });\r", "  });\r", "} else {\r", "  // This test runs only if the 'data' array is empty, confirming it's a valid state.\r", "  pm.test(\"Data array is empty, skipping alias-specific tests\", function () {\r", "    pm.expect(aliases).to.be.an(\"array\").with.lengthOf(0);\r", "  });\r", "}\r", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/api/aliases/host_user/{{lookup_host_user_id}}", "host": ["{{base_url}}"], "path": ["api", "aliases", "host_user", "{{lookup_host_user_id}}"]}}, "response": []}], "description": "Endpoints for retrieving existing aliases"}, {"name": "Update Aliases", "item": [{"name": "Update <PERSON><PERSON> Sender Mail", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test for status code\r", "pm.test(\"Status code is 200\", () => {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Test for response time\r", "pm.test(\"Response time is less than 200ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "// Test for content type\r", "pm.test(\"Content-Type is application/json\", () => {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.eql('application/json');\r", "});\r", "\r", "// Test for success field in response\r", "pm.test(\"Response has success field\", () => {\r", "    pm.expect(response).to.have.property('success');\r", "    pm.expect(response.success).to.be.a('boolean');\r", "});\r", "\r", "// Test for data field in response\r", "pm.test(\"Response data field is null\", () => {\r", "    pm.expect(response).to.have.property('data').that.is.null;\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"alias_id\": \"<EMAIL>\",\n    \"sender_email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/alias/sender_email", "host": ["{{base_url}}"], "path": ["api", "alias", "sender_email"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "C<PERSON> <PERSON><PERSON> - <PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response should have status 400 Bad Request\", function () {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "pm.test(\r", "  \"Response body should have the correct error structure\",\r", "  function () {\r", "    const responseData = pm.response.json();\r", "\r", "    // 1. Check the top-level fields\r", "    pm.expect(\r", "      responseData.success,\r", "      \"The 'success' field should be false\"\r", "    ).to.be.false;\r", "    pm.expect(responseData.data, \"The 'data' field should be null\").to.be.null;\r", "    pm.expect(responseData.error, \"The 'error' object should exist\").to.be.an(\r", "      \"object\"\r", "    ).and.not.to.be.null;\r", "  }\r", ");\r", "\r", "pm.test(\"Error object contains the correct validation details\", function () {\r", "  const responseData = pm.response.json();\r", "  const error = responseData.error;\r", "\r", "  // 2. Check the error code\r", "  pm.expect(error.code, \"Error code should be 'VALIDATION_ERROR'\").to.equal(\r", "    \"VALIDATION_ERROR\"\r", "  );\r", "\r", "  // 3. Check the error message for key details\r", "  // It's better to check for substrings than an exact match,\r", "  // as exact error messages can sometimes change.\r", "  pm.expect(\r", "    error.message,\r", "    \"Error message should be a string\"\r", "  ).to.be.a(\"string\");\r", "  pm.expect(\r", "    error.message,\r", "    \"Error message should mention the field that failed\"\r", "  ).to.include(\"OriginalRecipientEmail\");\r", "  pm.expect(\r", "    error.message,\r", "    \"Error message should mention the validation type\"\r", "  ).to.include(\"failed on the 'email' tag\");\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}, {"key": "X-API-Key", "value": "test-service-api-key-12345", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"original_recipient_email\": \"invalid-email\",\n    \"locator\": \"ERR123456\",\n    \"purpose\": \"system_booking_confirmation\",\n    \"channel\": 1,\n    \"communication_direction\": \"outbound\",\n    \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}, "description": "Test validation error for invalid email format"}, "response": []}, {"name": "Create <PERSON><PERSON> - <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"original_recipient_email\": \"<EMAIL>\",\n    \"locator\": \"ERR789012\",\n    \"purpose\": \"invalid_purpose\",\n    \"channel\": 1,\n    \"communication_direction\": \"outbound\",\n    \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}, "description": "Test validation error for invalid purpose"}, "response": []}], "description": "Test cases for error scenarios and validation"}, {"name": "JWT Service", "item": [{"name": "Generate Token", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "test-service-api-key-12345", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"service_name\": \"test-service\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/token", "host": ["{{base_url}}"], "path": ["auth", "token"]}}, "response": []}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "test-service-api-key-12345", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_name\": \"test-service\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/token/refresh", "host": ["{{base_url}}"], "path": ["auth", "token", "refresh"]}}, "response": []}]}, {"name": "Random <PERSON><PERSON>", "item": [{"name": "Test Random Emails", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test for status code\r", "pm.test('Status code is 201', () => {\r", "    pm.expect(pm.response.code).to.equal(201);\r", "});\r", "\r", "// Test for response time\r", "pm.test('Response time is less than 300ms', () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(300);\r", "});\r", "\r", "// Test for content type\r", "pm.test('Content-Type is application/json', () => {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.equal('application/json');\r", "});\r", "\r", "// Test for success field\r", "pm.test('Success field is true', () => {\r", "    pm.expect(response.success).to.be.true;\r", "});\r", "\r", "// Test for data structure\r", "pm.test('Data structure contains required fields', () => {\r", "    const requiredFields = [\r", "        'alias_id',\r", "        'original_recipient_email',\r", "        'masked_recipient_email',\r", "        'locator',\r", "        'purpose',\r", "        'channel',\r", "        'communication_direction',\r", "        'creator_service',\r", "        'created_at',\r", "        'expires_at',\r", "        'status',\r", "        'product_type'\r", "    ];\r", "    \r", "    requiredFields.forEach(field => {\r", "        pm.expect(response.data).to.have.property(field);\r", "    });\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"original_recipient_email\": \"{{email}}\",\r\n    \"locator\": \"{{locator}}\",\r\n    \"purpose\": \"guest_host_chat_hotel\",\r\n    \"product_type\": \"hotel\",\r\n    \"channel\": 1,\r\n    \"communication_direction\": \"bidirectional\",\r\n    \"expires_at\": \"2025-09-09T23:59:59Z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/alias", "host": ["{{base_url}}"], "path": ["api", "alias"]}}, "response": []}, {"name": "Test Random Emails By Original", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.expect(pm.response.status).to.eql(\"OK\");\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "pm.test(\"Success flag is true\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.have.property('success');\r", "    pm.expect(responseData.success).to.be.true\r", "});\r", "\r", "// Helper function to safely normalize the response data into an array.\r", "// Handles cases where data is an object, an array, or null/undefined.\r", "function getAliasesAsArray(response) {\r", "  const responseData = response.json();\r", "  if (!responseData || !responseData.data) {\r", "    return []; // Return empty array if data property is missing or null\r", "  }\r", "  return Array.isArray(responseData.data)\r", "    ? responseData.data\r", "    : [responseData.data];\r", "}\r", "\r", "// This test runs on every response to ensure basic success criteria are met.\r", "pm.test(\"Response is successful (Status 200 OK and success:true)\", function () {\r", "  pm.response.to.have.status(200);\r", "  const responseData = pm.response.json();\r", "  pm.expect(responseData.success, \"The 'success' field should be true\").to.be\r", "    .true;\r", "  pm.expect(responseData, \"Response should have a 'data' property\").to.have.property(\r", "    \"data\"\r", "  );\r", "});\r", "\r", "// Normalize the data into an array for consistent processing.\r", "const aliases = getAliasesAsArray(pm.response);\r", "\r", "// --- Conditional Tests ---\r", "// These tests will only run if the 'data' array is not empty.\r", "if (aliases.length > 0) {\r", "  pm.test(\"All aliases have the required fields\", function () {\r", "    const requiredFields = [\r", "      \"alias_id\",\r", "      \"original_recipient_email\",\r", "      \"original_sender_email\",\r", "      \"masked_recipient_email\",\r", "      \"masked_sender_email\",\r", "      \"locator\",\r", "      \"guest_user_id\",\r", "      \"host_user_id\",\r", "      \"fingerprint\",\r", "      \"context_fingerprint\",\r", "      \"purpose\",\r", "      \"channel\",\r", "      \"communication_direction\",\r", "      \"creator_service\",\r", "      \"created_at\",\r", "      \"expires_at\",\r", "      \"status\",\r", "      \"last_used_at\",\r", "      \"product_type\",\r", "    ];\r", "\r", "    aliases.forEach(function (alias, index) {\r", "      pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.be.an(\"object\");\r", "      requiredFields.forEach(function (field) {\r", "        pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.have.property(field);\r", "      });\r", "    });\r", "  });\r", "\r", "  pm.test(\"Email fields are in a valid format for all aliases\", function () {\r", "    const emailRegex = /^[^\\s@]+@([^\\s@.]+\\.)+[^\\s@.]+$/;\r", "\r", "    aliases.forEach(function (alias, index) {\r", "      const emailFields = [\r", "        alias.original_recipient_email,\r", "        alias.original_sender_email,\r", "        alias.masked_recipient_email,\r", "        alias.masked_sender_email,\r", "      ];\r", "\r", "      emailFields.forEach(function (email) {\r", "        if (email) {\r", "          pm.expect(email).to.match(\r", "            emailRegex,\r", "            `Email format is invalid for alias at index ${index}: ${email}`\r", "          );\r", "        }\r", "      });\r", "    });\r", "  });\r", "\r", "  pm.test(\"Channel is a non-negative integer for all aliases\", function () {\r", "    aliases.forEach(function (alias, index) {\r", "      pm.expect(alias, `<PERSON><PERSON> at index ${index}`).to.have.property(\"channel\");\r", "      pm.expect(alias.channel, `Channel for alias at index ${index}`)\r", "        .to.be.a(\"number\")\r", "        .and.to.be.at.least(0);\r", "    });\r", "  });\r", "} else {\r", "  // This test runs only if the 'data' array is empty, confirming it's a valid state.\r", "  pm.test(\"Data array is empty, skipping alias-specific tests\", function () {\r", "    pm.expect(aliases).to.be.an(\"array\").with.lengthOf(0);\r", "  });\r", "}\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/aliases/original_recipient?original_recipient_email={{email}}", "host": ["{{base_url}}"], "path": ["api", "aliases", "original_recipient"], "query": [{"key": "original_recipient_email", "value": "{{email}}"}]}}, "response": []}]}, {"name": "Home", "event": [{"listen": "test", "script": {"exec": ["// Check if the response status is 200\r", "pm.test(\"Response status is 200\", () => {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Check if the response time is less than 200ms\r", "pm.test(\"Response time is less than 200ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "// Parse the HTML response\r", "const $ = require('cheerio').load(pm.response.text());\r", "\r", "// Check if the title of the page is correct\r", "pm.test(\"Page title is correct\", () => {\r", "    const title = $('title').text();\r", "    pm.expect(title).to.equal(\"Mail Alias Management API\");\r", "});\r", "\r", "// Check if the version is present in the system information\r", "pm.test(\"Version is present in system information\", () => {\r", "    const version = $('#center').find('li').first().text();\r", "    pm.expect(version).to.include(\"Version: 1.0.0\");\r", "});\r", "\r", "// Check if the content type is text/html\r", "pm.test(\"Content-Type is text/html\", () => {\r", "    pm.response.to.have.header(\"Content-Type\", \"text/html\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "alias_id", "value": "<EMAIL>", "type": "string"}, {"key": "lookup_alias_id", "value": "<EMAIL>", "type": "default"}, {"key": "lookup_contextfingerprint", "value": "", "type": "default"}, {"key": "lookup_locator", "value": "", "type": "default"}, {"key": "lookup_host_user_id", "value": "", "type": "default"}, {"key": "lookup_fingerprint", "value": "", "type": "default"}, {"key": "lookup_original_recipient", "value": "<EMAIL>", "type": "default"}]}