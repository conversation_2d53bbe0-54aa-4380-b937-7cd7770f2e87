package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/api"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/infrastructure/config"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/infrastructure/database/dynamodb"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/pkg/logger"
	"github.com/go-playground/validator/v10"
)

func main() {
	appCtx, appCancel := context.WithCancel(context.Background())
	defer appCancel()

	// Load configuration
	config, err := config.LoadDefaultConfig()
	if err != nil {
		log.Fatalf("Failed to load config %v", err)
	}

	// Setup logger
	appLogger := logger.New(logger.Config{
		Level:       config.Server.LoggerLevel,
		Environment: config.Server.Build,
		ServiceName: config.Server.ServiceName,
		Pretty:      config.Server.Debug,
	})

	// Setup DynamoDB repository with KMS encryption
	aliasRepository, err := dynamodb.NewAliasRepository(appCtx, &config.DynamoDB)
	if err != nil {
		log.Fatalf("Failed to create alias repository: %v", err)
	}

	validator := validator.New()

	router := api.NewRouter(api.RouterConfig{
		AliasRepository:  aliasRepository,
		Logger:           appLogger,
		AppConfig:        &config.App,
		ServerConfig:     &config.Server,
		Validator:        validator,
		MonitoringConfig: &config.Monitoring,
	})

	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", config.Server.Port),
		Handler:      router,
		ReadTimeout:  config.Server.ReadTimeout,
		WriteTimeout: config.Server.WriteTimeout,
		IdleTimeout:  config.Server.IdleTimeout,
	}

	// Start HTTP server in goroutine
	var wg sync.WaitGroup
	serverErr := make(chan error, 1)

	wg.Add(1)
	go func() {
		defer wg.Done()
		appLogger.Info().
			Str("service", config.Server.ServiceName).
			Str("version", config.App.Version).
			Str("environment", config.Server.Build).
			Int("port", config.Server.Port).
			Msg("Starting HTTP server")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			serverErr <- err
		}
	}()

	// Wait for shutdown signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)

	select {
	case <-quit:
		appLogger.Info().Msg("Shutdown signal received")
	case err := <-serverErr:
		appLogger.Error().Err(err).Msg("Server failed to start")
		return
	}

	// Begin graceful shutdown
	appLogger.Info().Msg("Starting graceful shutdown...")

	// Create shutdown context with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), config.Server.ShutdownTimeout)
	defer shutdownCancel()

	// Use WaitGroup to coordinate shutdown
	var shutdownWg sync.WaitGroup

	// Shutdown HTTP server
	shutdownWg.Add(1)
	go func() {
		defer shutdownWg.Done()
		if err := router.GracefulShutdown(shutdownCtx, server); err != nil {
			appLogger.Error().Err(err).Msg("HTTP server graceful shutdown failed")
		} else {
			appLogger.Info().Msg("HTTP server gracefully stopped")
		}
	}()

	// Shutdown cleanup service
	// shutdownWg.Add(1)
	// go func() {
	// 	defer shutdownWg.Done()
	// 	appLogger.Info().Msg("Stopping cleanup service...")

	// 	// Cancel app context to signal cleanup service to stop
	// 	appCancel()

	// 	// Wait for cleanup service to stop gracefully
	// 	cleanupService.Stop()
	// 	appLogger.Info().Msg("Cleanup service gracefully stopped")
	// }()

	// Wait for all services to shut down or timeout
	shutdownComplete := make(chan struct{})
	go func() {
		shutdownWg.Wait()
		close(shutdownComplete)
	}()

	select {
	case <-shutdownComplete:
		appLogger.Info().Msg("All services gracefully stopped")
	case <-shutdownCtx.Done():
		appLogger.Warn().Msg("Graceful shutdown timed out")
	}

	// Wait for the HTTP server goroutine to finish
	wg.Wait()

	appLogger.Info().Msg("Application shutdown complete")
}
