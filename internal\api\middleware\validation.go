package middleware

import (
	"context"
	"net/http"

	"github.com/go-playground/validator/v10"
)

func ValidationMiddleware(validator *validator.Validate) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Add validator to request context
			ctx := context.WithValue(r.Context(), "validator", validator)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// Helper function to get validator from context
func GetValidator(ctx context.Context) *validator.Validate {
	if v := ctx.Value("validator"); v != nil {
		return v.(*validator.Validate)
	}
	return nil
}
