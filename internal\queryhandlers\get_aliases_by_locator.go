package queryhandlers

import (
	"context"
	"crypto/sha256"
	"encoding/hex"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/queries"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
)

type GetAliasByLocatorAndPurposeQueryHandler struct {
	aliasRepo repository.AliasRepository
}

func NewGetAliasByLocatorAndPurposeQueryHandler(aliasRepo repository.AliasRepository) *GetAliasByLocatorAndPurposeQueryHandler {
	return &GetAliasByLocatorAndPurposeQueryHandler{aliasRepo: aliasRepo}
}

func (h *GetAliasByLocatorAndPurposeQueryHandler) Handle(ctx context.Context, query *queries.GetAliasByLocatorAndPurposeQuery) (*core.Alias, error) {

	locator := hashLocator(query.Locator)

	alias, err := h.aliasRepo.GetAliasByLocatorAndPurpose(ctx, locator, query.Purpose)
	if err != nil {
		return nil, err
	}

	aliases := core.FilterActiveAndNotExpiredAliases([]*core.Alias{alias})

	if len(aliases) == 0 {
		return nil, core.ErrAliasNotFound
	}

	return aliases[0], nil
}

func hashLocator(locator string) string {
	hash := sha256.New()
	hash.Write([]byte(locator))
	return hex.EncodeToString(hash.Sum(nil))
}
