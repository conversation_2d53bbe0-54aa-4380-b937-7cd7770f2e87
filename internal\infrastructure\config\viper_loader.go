package config

import (
	"strings"

	"github.com/spf13/viper"
)

type ViperLoader struct {
	configName string
	configType string
	paths      []string
	useEnv     bool
}

func NewViperLoader(configName, configType string, paths []string, useEnv bool) *ViperLoader {
	return &ViperLoader{
		configName: configName,
		configType: configType,
		paths:      paths,
		useEnv:     useEnv,
	}
}

func DefaultViperLoader() *ViperLoader {
	return NewViperLoader("config", "yaml", []string{"./configs", "../configs", "../../configs"}, true)
}

// EnvironmentViperLoader creates a ViperLoader for environment-specific configurations
func EnvironmentViperLoader(environment string) *ViperLoader {
	configPaths := []string{"./configs", "../configs", "../../configs"}
	return NewViperLoader(environment, "yaml", configPaths, true)
}

func (v *ViperLoader) Load(cfg interface{}) error {
	viper.SetConfigName(v.configName)
	viper.SetConfigType(v.configType)

	for _, path := range v.paths {
		viper.AddConfigPath(path)
	}

	if v.useEnv {
		viper.AutomaticEnv()
		// Enable environment variable substitution in config values
		viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
		// Enable substitution of environment variables in config
		viper.SetTypeByDefaultValue(true)
	}

	// Try to read the config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return err
		}
	}

	return viper.Unmarshal(cfg)
}
