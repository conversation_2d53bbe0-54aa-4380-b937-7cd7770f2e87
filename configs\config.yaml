server:
  serviceName: mail-alias-management
  debug: false
  loggerLevel: info
  build: production
  port: 5000
  shutdownTimeout: 30s
  readTimeout: 5s
  writeTimeout: 7s
  idleTimeout: 10s
app:
  version: 1.0.0
  relayDomain: relay.priceres.com # ej. booking, internal comunication, support
  clientDomain: relay.priceres.com #client.pricetravelmexico.mx #client.priceres.com # ej. hotel, flight comunication
  partnerDomain: relay.priceres.com #partner.priceres.com
  internalDomain: relay.priceres.com #privaterelay.pricetravel.com # internal comunication
  excludedDomains:
    - "pricetravel.com"
    - "tiquetesbaratos.com"
dynamodb:
  region: us-east-1
  table: EmailAliases
  enableTTL: true
  ttlGraceDays: 0
  encryption:
    enabled: true
    type: aes
    keys:
      v1: "8SXP3234O+QU3QWxNEQWvlRRkSVAlbMNOtucP5TEj2w=" # rm for production use
      v2: "fOu8cIm6NGPC2W60m1MzrjunHV/nvALkl/83NAowgwI="
monitoring:
  enabled: true
