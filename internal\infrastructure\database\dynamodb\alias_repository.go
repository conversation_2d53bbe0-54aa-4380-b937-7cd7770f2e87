package dynamodb

import (
	"context"
	"fmt"
	"time"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

// convertToTTLWithGrace converts a time.Time to DynamoDB TTL format with grace period
func (r *dynamoDBAliasRepository) convertToTTLWithGrace(expiresAt time.Time) *types.AttributeValueMemberN {
	// Add grace period to expiration time
	ttlTime := expiresAt.AddDate(0, 0, r.ttlGraceDays)
	return &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", ttlTime.Unix())}
}

// decryptAlias decrypts sensitive fields in an alias
func (r *dynamoDBAliasRepository) decryptAlias(ctx context.Context, alias *core.Alias) error {
	// Decrypt original recipient email
	decryptedRecipient, err := r.encryption.DecryptField(alias.OriginalRecipientEmail)
	if err != nil {
		return fmt.Errorf("failed to decrypt original recipient email: %w", err)
	}
	alias.OriginalRecipientEmail = decryptedRecipient

	// Decrypt original sender email if present
	if alias.OriginalSenderEmail != nil {
		decryptedSender, err := r.encryption.DecryptEmail(alias.OriginalSenderEmail)
		if err != nil {
			return fmt.Errorf("failed to decrypt original sender email: %w", err)
		}
		alias.OriginalSenderEmail = decryptedSender
	}

	return nil
}

// decryptAliases decrypts sensitive fields in a slice of aliases
func (r *dynamoDBAliasRepository) decryptAliases(ctx context.Context, aliases []*core.Alias) error {
	for _, alias := range aliases {
		if err := r.decryptAlias(ctx, alias); err != nil {
			return err
		}
	}
	return nil
}

// dynamoDBAliasRepository implements the AliasRepository interface for DynamoDB.
type dynamoDBAliasRepository struct {
	client       *dynamodb.Client
	tableName    string
	enableTTL    bool
	ttlGraceDays int
	encryption   *AESEncryptionService
}

// NewDynamoDBAliasRepository creates a new instance of DynamoDBAliasRepository.
func NewDynamoDBAliasRepository(client *dynamodb.Client, tableName string, enableTTL bool, ttlGraceDays int, encryption *AESEncryptionService) repository.AliasRepository {
	return &dynamoDBAliasRepository{
		client:       client,
		tableName:    tableName,
		enableTTL:    enableTTL,
		ttlGraceDays: ttlGraceDays,
		encryption:   encryption,
	}
}

// SaveAlias implements repository.AliasRepository.
func (r *dynamoDBAliasRepository) SaveAlias(ctx context.Context, alias *core.Alias) error {
	// Create a copy to avoid modifying the original
	aliasCopy := *alias

	// Encrypt sensitive fields before storing
	encryptedRecipient, err := r.encryption.EncryptField(aliasCopy.OriginalRecipientEmail)
	if err != nil {
		return fmt.Errorf("failed to encrypt original recipient email: %w", err)
	}
	aliasCopy.OriginalRecipientEmail = encryptedRecipient

	if aliasCopy.OriginalSenderEmail != nil {
		encryptedSender, err := r.encryption.EncryptEmail(aliasCopy.OriginalSenderEmail)
		if err != nil {
			return fmt.Errorf("failed to encrypt original sender email: %w", err)
		}
		aliasCopy.OriginalSenderEmail = encryptedSender
	}

	item, err := attributevalue.MarshalMap(&aliasCopy)
	if err != nil {
		return fmt.Errorf("failed to marshal alias: %w", err)
	}

	// Add TTL attribute using expires_at field if TTL is enabled
	if r.enableTTL {
		item["ttl"] = r.convertToTTLWithGrace(alias.ExpiresAt)
	}

	putItemInput := &dynamodb.PutItemInput{
		TableName: aws.String(r.tableName),
		Item:      item,
		// Optional: ConditionExpression for preventing overwrites if alias_id already exists with same context_fingerprint
		// ConditionExpression: aws.String("attribute_not_exists(alias_id)"), // For primary key uniqueness
	}

	_, err = r.client.PutItem(ctx, putItemInput)
	if err != nil {
		// Example of checking for conditional write failures if you add ConditionExpression
		if _, ok := err.(*types.ConditionalCheckFailedException); ok {
			return core.ErrAliasConflict // Use your custom error
		}
		return fmt.Errorf("failed to put item to DynamoDB: %w", err)
	}
	return nil
}

// GetAliasByID thats active and hasnt expired implements repository.AliasRepository.
func (r *dynamoDBAliasRepository) GetAliasByID(ctx context.Context, aliasID string) (*core.Alias, error) {
	getItemInput := &dynamodb.GetItemInput{
		TableName: aws.String(r.tableName),
		Key: map[string]types.AttributeValue{
			"alias_id": &types.AttributeValueMemberS{Value: aliasID},
		},
	}

	result, err := r.client.GetItem(ctx, getItemInput)
	if err != nil {
		return nil, fmt.Errorf("failed to get item from DynamoDB: %w", err)
	}
	if result.Item == nil {
		return nil, core.ErrAliasNotFound // Use your custom error
	}

	var alias core.Alias
	err = attributevalue.UnmarshalMap(result.Item, &alias)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal item: %w", err)
	}

	// Decrypt sensitive fields after retrieving
	if err := r.decryptAlias(ctx, &alias); err != nil {
		return nil, err
	}

	return &alias, nil
}

func (r *dynamoDBAliasRepository) GetAliasesByGuestUserID(ctx context.Context, guestUserID string, limit, offset int) ([]*core.Alias, error) {
	queryInput := &dynamodb.QueryInput{
		TableName:              aws.String(r.tableName),
		IndexName:              aws.String("GuestAliasesIndex"),
		KeyConditionExpression: aws.String("#guestId = :guestId"),
		ExpressionAttributeNames: map[string]string{
			"#guestId": "guest_user_id",
		},
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":guestId": &types.AttributeValueMemberS{Value: guestUserID},
		},
		Limit:            aws.Int32(int32(limit)),
		ScanIndexForward: aws.Bool(true),
	}

	result, err := r.client.Query(ctx, queryInput)
	if err != nil {
		return nil, fmt.Errorf("failed to query aliases by guest user ID: %w", err)
	}

	var aliases []*core.Alias
	err = attributevalue.UnmarshalListOfMaps(result.Items, &aliases)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal aliases: %w", err)
	}

	// Decrypt sensitive fields
	if err := r.decryptAliases(ctx, aliases); err != nil {
		return nil, err
	}

	return aliases, nil
}

// GetAliasesByLocator implements repository.AliasRepository.
func (r *dynamoDBAliasRepository) GetAliasByLocatorAndPurpose(ctx context.Context, locator, purpose string) (*core.Alias, error) {
	queryInput := &dynamodb.QueryInput{
		TableName:              aws.String(r.tableName),
		IndexName:              aws.String("LocatorPurposeIndex"),
		KeyConditionExpression: aws.String("#locator = :locator"),
		ExpressionAttributeNames: map[string]string{
			"#locator": "locator",
		},
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":locator": &types.AttributeValueMemberS{Value: locator},
		},
		Limit:            aws.Int32(int32(1)),
		ScanIndexForward: aws.Bool(true),
	}

	if purpose != "" {
		queryInput.KeyConditionExpression = aws.String("#locator = :locator AND #purpose = :purpose")
		queryInput.ExpressionAttributeNames["#purpose"] = "purpose"
		queryInput.ExpressionAttributeValues[":purpose"] = &types.AttributeValueMemberS{Value: purpose}
	}

	result, err := r.client.Query(ctx, queryInput)
	if err != nil {
		return nil, fmt.Errorf("failed to query aliases by locator: %w", err)
	}

	if len(result.Items) == 0 {
		return nil, core.ErrAliasNotFound
	}

	var alias core.Alias
	err = attributevalue.UnmarshalMap(result.Items[0], &alias)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal aliases: %w", err)
	}

	// Decrypt sensitive fields
	if err := r.decryptAlias(ctx, &alias); err != nil {
		return nil, err
	}

	return &alias, nil
}

func (r *dynamoDBAliasRepository) GetAliasesByHostUserID(ctx context.Context, hostUserID string, limit, offset int) ([]*core.Alias, error) {
	queryInput := &dynamodb.QueryInput{
		TableName:              aws.String(r.tableName),
		IndexName:              aws.String("HostUserIdIndex"),
		KeyConditionExpression: aws.String("#host_user_id = :host_user_id"),
		ExpressionAttributeNames: map[string]string{
			"#host_user_id": "host_user_id",
		},
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":host_user_id": &types.AttributeValueMemberS{Value: hostUserID},
		},
		Limit:            aws.Int32(int32(limit)),
		ScanIndexForward: aws.Bool(true),
	}

	result, err := r.client.Query(ctx, queryInput)
	if err != nil {
		return nil, fmt.Errorf("failed to query aliases by host user ID: %w", err)
	}

	var aliases []*core.Alias
	err = attributevalue.UnmarshalListOfMaps(result.Items, &aliases)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal aliases: %w", err)
	}

	// Decrypt sensitive fields
	if err := r.decryptAliases(ctx, aliases); err != nil {
		return nil, err
	}

	return aliases, nil
}

func (r *dynamoDBAliasRepository) GetAliasByContextFingerprint(ctx context.Context, fingerprint string) (*core.Alias, error) {
	queryInput := &dynamodb.QueryInput{
		TableName:              aws.String(r.tableName),
		IndexName:              aws.String("ContextFingerprintIndex"),
		KeyConditionExpression: aws.String("#contextFingerprint = :contextFingerprint"),
		ExpressionAttributeNames: map[string]string{
			"#contextFingerprint": "context_fingerprint",
		},
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":contextFingerprint": &types.AttributeValueMemberS{Value: fingerprint},
		},
		Limit: aws.Int32(int32(1)),
	}

	result, err := r.client.Query(ctx, queryInput)
	if err != nil {
		return nil, fmt.Errorf("failed to query aliases by context fingerprint: %w", err)
	}

	if len(result.Items) == 0 {
		return nil, core.ErrAliasNotFound
	}

	var alias core.Alias
	err = attributevalue.UnmarshalMap(result.Items[0], &alias)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal alias: %w", err)
	}

	// Decrypt sensitive fields
	if err := r.decryptAlias(ctx, &alias); err != nil {
		return nil, err
	}

	return &alias, nil
}

func (r *dynamoDBAliasRepository) GetAliasesByLegacyFingerprint(ctx context.Context, fingerprint string, limit, offset int) ([]*core.Alias, error) {
	queryInput := &dynamodb.QueryInput{
		TableName:              aws.String(r.tableName),
		IndexName:              aws.String("FingerprintIndex"),
		KeyConditionExpression: aws.String("#fingerprint = :fingerprint"),
		ExpressionAttributeNames: map[string]string{
			"#fingerprint": "fingerprint",
		},
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":fingerprint": &types.AttributeValueMemberS{Value: fingerprint},
		},
		Limit:            aws.Int32(int32(limit)),
		ScanIndexForward: aws.Bool(true),
	}

	result, err := r.client.Query(ctx, queryInput)
	if err != nil {
		return nil, fmt.Errorf("failed to query aliases by legacy fingerprint: %w", err)
	}

	var aliases []*core.Alias
	err = attributevalue.UnmarshalListOfMaps(result.Items, &aliases)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal aliases: %w", err)
	}

	// Decrypt sensitive fields
	if err := r.decryptAliases(ctx, aliases); err != nil {
		return nil, err
	}

	return aliases, nil
}

// Update Func to add sender_email to the alias
func (r *dynamoDBAliasRepository) UpdateAliasSenderEmail(ctx context.Context, alias *core.Alias, senderEmail string, maskedSenderEmail string) (*core.Alias, error) {
	// Encrypt the sender email before storing
	encryptedSenderEmail, err := r.encryption.EncryptField(senderEmail)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt sender email: %w", err)
	}

	updateExpression := "SET #original_sender_email = :original_sender_email, #masked_sender_email = :masked_sender_email"
	expressionAttributeNames := map[string]string{
		"#original_sender_email": "original_sender_email",
		"#masked_sender_email":   "masked_sender_email",
	}
	expressionAttributeValues := map[string]types.AttributeValue{
		":original_sender_email": &types.AttributeValueMemberS{Value: encryptedSenderEmail},
		":masked_sender_email":   &types.AttributeValueMemberS{Value: maskedSenderEmail},
	}

	// Add TTL to update if TTL is enabled
	if r.enableTTL {
		updateExpression += ", #ttl = :ttl"
		expressionAttributeNames["#ttl"] = "ttl"
		expressionAttributeValues[":ttl"] = r.convertToTTLWithGrace(alias.ExpiresAt)
	}

	updateItemInput := &dynamodb.UpdateItemInput{
		TableName: aws.String(r.tableName),
		Key: map[string]types.AttributeValue{
			"alias_id": &types.AttributeValueMemberS{Value: alias.AliasID},
		},
		UpdateExpression:          aws.String(updateExpression),
		ExpressionAttributeNames:  expressionAttributeNames,
		ExpressionAttributeValues: expressionAttributeValues,
	}

	_, err = r.client.UpdateItem(ctx, updateItemInput)
	if err != nil {
		return nil, fmt.Errorf("failed to update alias sender email: %w", err)
	}

	updatedAlias := alias
	updatedAlias.OriginalSenderEmail = &senderEmail
	updatedAlias.MaskedSenderEmail = &maskedSenderEmail

	return updatedAlias, nil
}
