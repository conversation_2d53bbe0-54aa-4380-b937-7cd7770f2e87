package queryhandlers

import (
	"context"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core/fingerprint"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/queries"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
)

type GetAliasesByOriginalRecipientEmailQueryHandler struct {
	aliasRepo            repository.AliasRepository
	fingerprintGenerator *fingerprint.FingerprintGenerator
}

func NewGetAliasesByOriginalRecipientEmailQueryHandler(aliasRepo repository.AliasRepository, fingerprintGenerator *fingerprint.FingerprintGenerator) *GetAliasesByOriginalRecipientEmailQueryHandler {
	return &GetAliasesByOriginalRecipientEmailQueryHandler{aliasRepo: aliasRepo, fingerprintGenerator: fingerprintGenerator}
}

func (h *GetAliasesByOriginalRecipientEmailQueryHandler) Handle(ctx context.Context, query *queries.GetAliasesByOriginalRecipientEmailQuery) ([]*core.Alias, error) {

	fingerprint, err := h.fingerprintGenerator.GenerateLegacyFingerprint(query.OriginalRecipientEmail)
	if err != nil {
		return nil, err
	}

	aliases, err := h.aliasRepo.GetAliasesByLegacyFingerprint(ctx, fingerprint, query.Limit, query.Offset)
	if err != nil {
		return nil, err
	}

	aliases = core.FilterActiveAndNotExpiredAliases(aliases)

	if len(aliases) == 0 {
		return nil, core.ErrAliasNotFound
	}

	return aliases, nil
}
