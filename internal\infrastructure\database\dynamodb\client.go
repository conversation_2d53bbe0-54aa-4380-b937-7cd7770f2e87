package dynamodb

import (
	"context"
	"fmt"

	appConfig "a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/infrastructure/config"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
)

// NewDynamoDBClient creates a new DynamoDB client.
// It loads default AWS config and allows specifying a custom endpoint for local development (e.g., DynamoDB Local).
func NewDynamoDBClient(ctx context.Context, dynamoDBConfig *appConfig.DynamoDBConfig) (*dynamodb.Client, error) {
	// Load default AWS config, which looks for credentials in environment variables,
	// shared credentials file, ECS/EC2 instance roles, etc.
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion(dynamoDBConfig.Region))
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS SDK config: %w", err)
	}

	// Override endpoint for local development if provided
	customEndpoint := dynamoDBConfig.CustomEndpoint
	if customEndpoint != "" {
		cfg.EndpointResolverWithOptions = aws.EndpointResolverWithOptionsFunc(
			func(service, region string, options ...interface{}) (aws.Endpoint, error) {
				return aws.Endpoint{URL: customEndpoint}, nil
			},
		)
	}

	return dynamodb.NewFromConfig(cfg), nil
}

// NewAliasRepository creates a new alias repository with AES encryption
func NewAliasRepository(ctx context.Context, dynamoDBConfig *appConfig.DynamoDBConfig) (repository.AliasRepository, error) {
	// Create DynamoDB client
	client, err := NewDynamoDBClient(ctx, dynamoDBConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create DynamoDB client: %w", err)
	}

	// Get encryption key from config
	encryptionKeys := dynamoDBConfig.Encryption.Keys

	// Create AES encryption service with key from config
	aesEncryption, err := NewAESEncryptionService(ctx, encryptionKeys, "v2")
	if err != nil {
		return nil, fmt.Errorf("failed to create AES encryption service: %w", err)
	}

	// Create and return repository
	return NewDynamoDBAliasRepository(
		client,
		dynamoDBConfig.Table,
		dynamoDBConfig.EnableTTL,
		dynamoDBConfig.TTLGraceDays,
		aesEncryption,
	), nil
}
