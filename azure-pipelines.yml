# Docker
# Build a Docker image
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
  - master

resources:
  - repo: self

variables:
  imageTag: "$(Build.BuildId)"
  awsServiceConnection: "AwsDeploy-PROD-OPE-************"
  awsAccountId: "************"
  awsRegion: "us-east-1"
  imageRepository: "mail-alias-management"
  fullEcrRepositoryUri: "$(awsAccountId).dkr.ecr.$(awsRegion).amazonaws.com/$(imageRepository)"

stages:
  - stage: Build
    displayName: "Build and Push to ECR"
    jobs:
      - job: Build
        displayName: Build
        pool:
          name: default
        steps:
          - task: AWSShellScript@1
            displayName: "Login to Amazon ECR"
            inputs:
              awsCredentials: $(awsServiceConnection)
              regionName: $(awsRegion)
              scriptType: "inline"
              inlineScript: |
                aws ecr get-login-password --region $(awsRegion) | docker login --username AWS --password-stdin $(awsAccountId).dkr.ecr.$(awsRegion).amazonaws.com

          - task: Docker@2
            displayName: "Build Docker image"
            inputs:
              command: build
              dockerfile: "$(Build.SourcesDirectory)/Dockerfile"
              repository: $(imageRepository)
              tags: |
                $(imageTag)

          - task: Docker@2
            displayName: "Tag image with full ECR URI"
            inputs:
              command: "tag"
              arguments: "$(imageRepository):$(imageTag) $(fullEcrRepositoryUri):$(imageTag)"

          - task: Docker@2
            displayName: "Push image to ECR"
            inputs:
              command: "push"
              repository: "$(fullEcrRepositoryUri)"
              tags: |
                $(imageTag)
