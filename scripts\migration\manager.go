package main

import (
	"context"
	"fmt"
	"time"

	"github.com/rs/zerolog"
)

// Manager handles database connection lifecycle
type Manager struct {
	pool   *Pool
	config *Config
	logger *zerolog.Logger
}

// NewManager creates a new database manager
func NewManager(config *Config, logger *zerolog.Logger) *Manager {
	return &Manager{
		config: config,
		logger: logger,
	}
}

// Connect establishes database connection with retry logic
func (m *Manager) Connect(ctx context.Context) error {
	// Create connection pool
	pool, err := NewPool(ctx, m.config, m.logger)
	if err != nil {
		return fmt.Errorf("failed to create database pool: %w", err)
	}

	// Connect with retry logic
	maxRetries := 5
	retryDelay := time.Second * 2

	for attempt := 1; attempt <= maxRetries; attempt++ {
		if err := pool.Connect(ctx); err != nil {
			if attempt == maxRetries {
				pool.Close()
				return fmt.Errorf("failed to connect to database after %d attempts: %w", maxRetries, err)
			}

			m.logger.Warn().
				Err(err).
				Int("attempt", attempt).
				Dur("retry_delay", retryDelay).
				Msg("Database connection failed, retrying...")

			time.Sleep(retryDelay)
			retryDelay *= 2 // Exponential backoff
			continue
		}

		// Connection successful
		m.pool = pool

		return nil
	}

	return fmt.Errorf("unexpected error in connection retry loop")
}

// GetPool returns the database pool
func (m *Manager) GetPool() *Pool {
	return m.pool
}

// Ping tests the database connection
func (m *Manager) Ping(ctx context.Context) error {
	if m.pool == nil {
		return fmt.Errorf("database pool not initialized")
	}
	return m.pool.Ping(ctx)
}

// HealthCheck performs a comprehensive health check
func (m *Manager) HealthCheck(ctx context.Context) error {
	if m.pool == nil {
		return fmt.Errorf("database pool not initialized")
	}
	return m.pool.HealthCheck(ctx)
}

// GetStats returns database connection statistics
func (m *Manager) GetStats() map[string]interface{} {
	if m.pool == nil {
		return map[string]interface{}{
			"status": "not_initialized",
		}
	}
	return m.pool.GetStats()
}

// Close closes the database connection
func (m *Manager) Close() {
	if m.pool != nil {
		m.pool.Close()
		m.pool = nil
	}
}

// IsConnected checks if the database is connected
func (m *Manager) IsConnected() bool {
	return m.pool != nil
}
