package queryhandlers

import (
	"context"

	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/core"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/queries"
	"a-devops.pricetravel.com.mx/DefaultCollection/Operaciones/_git/mail_alias_management/internal/repository"
)

type GetAliasByIDQueryHandler struct {
	aliasRepo repository.AliasRepository
}

func NewGetAliasByIDQueryHandler(aliasRepo repository.AliasRepository) *GetAliasByIDQueryHandler {
	return &GetAliasByIDQueryHandler{aliasRepo: aliasRepo}
}

func (h *GetAliasByIDQueryHandler) Handle(ctx context.Context, query *queries.GetAliasByIDQuery) (*core.Alias, error) {

	alias, err := h.aliasRepo.GetAliasByID(ctx, query.AliasID)
	if err != nil {
		return nil, err
	}

	aliases := core.FilterActiveAndNotExpiredAliases([]*core.<PERSON>as{alias})

	if len(aliases) == 0 {
		return nil, core.ErrAliasNotFound
	}

	return aliases[0], nil
}
